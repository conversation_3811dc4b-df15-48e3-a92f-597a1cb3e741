import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, ImageBackground, TouchableOpacity, Dimensions, Image } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { useRouter } from 'expo-router';
import { Colors } from '../constants/Colors';

const { width } = Dimensions.get('window');

const optionsData = [
  {
    background: 'https://66.media.tumblr.com/6fb397d822f4f9f4596dff2085b18f2e/tumblr_nzsvb4p6xS1qho82wo1_1280.jpg',
    icon: 'walking',
    main: '溪谷',
    sub: 'Omuke trughte a otufta',
  },
  {
    background: 'https://66.media.tumblr.com/8b69cdde47aa952e4176b4200052abf4/tumblr_o51p7mFFF21qho82wo1_1280.jpg',
    icon: 'snowflake',
    main: '雪地',
    sub: 'Omuke trughte a otufta',
  },
  {
    background: 'https://66.media.tumblr.com/5af3f8303456e376ceda1517553ba786/tumblr_o4986gakjh1qho82wo1_1280.jpg',
    icon: 'tree',
    main: '森林',
    sub: 'Omuke trughte a otufta',
  },
  {
    background: 'https://66.media.tumblr.com/5516a22e0cdacaa85311ec3f8fd1e9ef/tumblr_o45jwvdsL11qho82wo1_1280.jpg',
    icon: 'tint',
    main: 'Idiefe',
    sub: 'Omuke trughte a otufta',
  },
  {
    background: 'https://66.media.tumblr.com/f19901f50b79604839ca761cd6d74748/tumblr_o65rohhkQL1qho82wo1_1280.jpg',
    icon: 'sun',
    main: '沙漠',
    sub: 'Omuke trughte a otufta',
  },
];

const CARD_WIDTH = width * 0.7;

export default function CommunityCareer() {
  const [activeIndex, setActiveIndex] = useState(0);
  const router = useRouter();
  const theme = Colors.light;

  const renderItem = ({ item, index }) => {
    const isActive = index === activeIndex;
    return (
      <TouchableOpacity onPress={() => setActiveIndex(index)}>
        <ImageBackground
          source={{ uri: item.background }}
          style={[styles.option, isActive && styles.activeOption]}
          imageStyle={{ borderRadius: 16 }}
        >
          <View style={styles.shadow} />
          <View style={styles.label}>
            <View style={styles.icon}>
              <FontAwesome5 name={item.icon} size={20} color="white" />
            </View>
            <View style={styles.info}>
              <Text style={styles.main}>{item.main}</Text>
              <Text style={styles.sub}>{item.sub}</Text>
            </View>
          </View>
        </ImageBackground>
      </TouchableOpacity>
    );
  };

  return (

    <ThemedView style={{ flex: 1, backgroundColor: '#FFF8F3' }}>
      {/* 顶部标题栏 */}
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <TouchableOpacity onPress={() => router.back()}>
          <Image source={require('../assets/FrameTwo.png')} style={styles.backIcon} />
        </TouchableOpacity>
        <ThemedText style={[styles.headerTitle, { color: theme.title }]}>职业生涯规划</ThemedText>
        <TouchableOpacity>
          <ThemedText style={[styles.headerMenu, { color: theme.iconColor }]}>⋯</ThemedText>
        </TouchableOpacity>
      </View>

      {/* 卡片选择器 */}
      <FlatList
        data={optionsData}
        horizontal
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItem}
        contentContainerStyle={styles.container}
        showsHorizontalScrollIndicator={false}
      />
    </ThemedView>

  );
}



const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 16,
    marginBottom: 8
  },
  backIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
    resizeMode: 'contain'
  },
  headerTitle: {
    flex: 1,
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center'
  },
  headerMenu: {
    fontSize: 24
  },
  container: {
    paddingHorizontal: 16,
    paddingVertical: 40,
  },
  option: {
    width: CARD_WIDTH,
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    marginRight: 16,
    justifyContent: 'flex-end',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  activeOption: {
    transform: [{ scale: 1.05 }],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
  },
  shadow: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  label: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  icon: {
    marginRight: 10,
  },
  info: {},
  main: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  sub: {
    color: 'white',
    fontSize: 12,
    opacity: 0.8,
  },
}); 