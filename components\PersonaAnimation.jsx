import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity, StyleSheet } from 'react-native';
import ThemedText from './ThemedText';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  withSequence,
  Easing,
  runOnJS,
  interpolate,
  cancelAnimation
} from 'react-native-reanimated';

const PersonaAnimation = ({
  avatarSource,
  isExpanded: externalIsExpanded,
  onExpand: externalOnExpand,
  onCollapse: externalOnCollapse
}) => {
  // 主旋转动画值（reanimated实现）
  const rotate = useSharedValue(0);

  // 弹出动画状态 - 可以由外部控制或内部控制
  const [internalIsExpanded, setInternalIsExpanded] = useState(false);
  const isExpanded = externalIsExpanded !== undefined ? externalIsExpanded : internalIsExpanded;

  // 弹出动画值
  const expandAnim = useSharedValue(0);
  // 标签透明度动画值
  const fadeAnim = useSharedValue(0);
  // 脉冲波浪动画值 - 用于创造层次感
  const pulseWave1 = useSharedValue(0);
  const pulseWave2 = useSharedValue(0);
  const pulseWave3 = useSharedValue(0);
  
  // 虚线圆圈和标签的配置
  const dashedCircleRadius = 140; // 虚线圆圈半径 (280/2)
  const smallCircleRadius = 80; // 小圆圈轨道半径
  const tagRadius = 40; // 标签圆圈半径 (80/2)
  const tags = [
    { text: '焦虑的进取者', angle: 270 }, // 顶部 (12点方向)
    { text: '群体学习', angle: 330 }, // 右上 (2点方向)
    { text: '多维发展需求者', angle: 30 }, // 右下 (4点方向)
    { text: '基础知识薄弱', angle: 90 }, // 底部 (6点方向)
    { text: '时间管理', angle: 150 }, // 左下 (8点方向)
    { text: '技能机制型学习者', angle: 210 } // 左上 (10点方向)
  ];

  // 小圆圈配置 - 形成一个圆形分布
  const smallCircles = [
    { text: '职业', angle: 0 }, // 右侧 (3点方向)
    { text: '实操', angle: 120 }, // 左下 (8点方向)
    { text: '资源', angle: 240 } // 左上 (10点方向)
  ];

  // 展开动画函数 - 脉冲波浪效果
  const handleExpand = () => {
    console.log('PersonaAnimation - 开始展开动画（脉冲波浪效果）');

    // 如果有外部控制函数，调用它；否则使用内部状态
    if (externalOnExpand) {
      externalOnExpand();
    } else {
      setInternalIsExpanded(true);
    }

    // 移除旋转偏移记录，使用新的平滑过渡方案

    // 重置所有动画值
    fadeAnim.value = 0;
    expandAnim.value = 0;
    pulseWave1.value = 0;
    pulseWave2.value = 0;
    pulseWave3.value = 0;

    // 创造脉冲波浪效果 - 从中心向外扩散
    // 第一波：虚线圆圈出现
    expandAnim.value = withTiming(1, {
      duration: 600,
      easing: Easing.out(Easing.quad)
    });

    // 第二波：第一层标签出现（延迟100ms）
    pulseWave1.value = withDelay(100, withTiming(1, {
      duration: 500,
      easing: Easing.out(Easing.quad) // 改为更稳定的quad easing
    }));

    // 第三波：第二层标签出现（延迟200ms）
    pulseWave2.value = withDelay(200, withTiming(1, {
      duration: 500,
      easing: Easing.out(Easing.quad)
    }));

    // 第四波：第三层标签出现（延迟300ms）
    pulseWave3.value = withDelay(300, withTiming(1, {
      duration: 500,
      easing: Easing.out(Easing.quad)
    }));

    // 最后：所有标签淡入（延迟150ms开始）
    fadeAnim.value = withDelay(150, withTiming(1, {
      duration: 600,
      easing: Easing.out(Easing.quad)
    }));
  };

  // 收缩动画函数 - 展开动画的反向过程
  const handleCollapse = () => {
    console.log('PersonaAnimation - 开始收起动画（反向展开）');

    // 收起动画：按展开的反向顺序进行
    // 第一波：第三层标签先消失（反向第四波）
    pulseWave3.value = withTiming(0, {
      duration: 500,
      easing: Easing.out(Easing.quad)
    });

    // 第二波：第二层标签消失（延迟100ms，反向第三波）
    pulseWave2.value = withDelay(100, withTiming(0, {
      duration: 500,
      easing: Easing.out(Easing.quad)
    }));

    // 第三波：第一层标签消失（延迟200ms，反向第二波）
    pulseWave1.value = withDelay(200, withTiming(0, {
      duration: 500,
      easing: Easing.out(Easing.quad)
    }));

    // 第四波：标签淡出（延迟150ms）
    fadeAnim.value = withDelay(150, withTiming(0, {
      duration: 600,
      easing: Easing.out(Easing.quad)
    }));

    // 最后：虚线圆圈收起（延迟300ms，反向第一波）
    expandAnim.value = withDelay(300, withTiming(0, {
      duration: 600,
      easing: Easing.out(Easing.quad)
    }, (finished) => {
      if (finished) {
        // 旋转偏移量已移除，使用新的平滑过渡方案

        if (externalOnCollapse) {
          runOnJS(externalOnCollapse)();
        } else {
          runOnJS(setInternalIsExpanded)(false);
        }
      }
    }));
  };

  // 手势处理已移至plans页面级别

  // 移除未使用的resetAnimations函数

  // 计算标签位置的算法 - 确保标签圆心在虚线圆圈上
  const calculateTagPosition = (angle) => {
    const radian = (angle * Math.PI) / 180;
    const x = dashedCircleRadius * Math.cos(radian);
    const y = dashedCircleRadius * Math.sin(radian);
    // 容器中心点是160,160
    const containerCenter = 160; // 320/2 = 160
    return {
      left: containerCenter + x - tagRadius, // 减去标签半径，让标签圆心在虚线圆圈上
      top: containerCenter + y - tagRadius   // Y轴向下为正
    };
  };

  // 计算小圆圈位置的算法 - 形成以中心为圆心的圆
  const calculateSmallCirclePosition = (angle) => {
    const radian = (angle * Math.PI) / 180;
    const x = smallCircleRadius * Math.cos(radian);
    const y = smallCircleRadius * Math.sin(radian);
    const containerCenter = 160; // 320/2 = 160
    const smallCircleHalfSize = 20; // 40/2 = 20
    return {
      left: containerCenter + x - smallCircleHalfSize,
      top: containerCenter + y - smallCircleHalfSize
    };
  };

  // 启动旋转动画
  useEffect(() => {
    rotate.value = withRepeat(
      withTiming(360, {
        duration: 20000,
        easing: Easing.linear
      }),
      -1
    );
  }, []);

  // 监听外部状态变化，同步动画 - 脉冲波浪效果
  useEffect(() => {
    if (externalIsExpanded !== undefined) {
      if (externalIsExpanded && !internalIsExpanded) {
        // 外部要求展开，但内部还未展开
        console.log('PersonaAnimation - 外部触发展开（脉冲波浪效果）');
        setInternalIsExpanded(true);

        // 移除旋转偏移记录，使用新的平滑过渡方案

        // 重置所有动画值
        fadeAnim.value = 0;
        expandAnim.value = 0;
        pulseWave1.value = 0;
        pulseWave2.value = 0;
        pulseWave3.value = 0;

        // 使用与handleExpand相同的脉冲波浪动画
        expandAnim.value = withTiming(1, {
          duration: 600,
          easing: Easing.out(Easing.quad)
        });

        pulseWave1.value = withDelay(100, withTiming(1, {
          duration: 500,
          easing: Easing.out(Easing.quad) // 改为更稳定的quad easing
        }));

        pulseWave2.value = withDelay(200, withTiming(1, {
          duration: 500,
          easing: Easing.out(Easing.quad)
        }));

        pulseWave3.value = withDelay(300, withTiming(1, {
          duration: 500,
          easing: Easing.out(Easing.quad)
        }));

        fadeAnim.value = withDelay(150, withTiming(1, {
          duration: 600,
          easing: Easing.out(Easing.quad)
        }));
      } else if (!externalIsExpanded && internalIsExpanded) {
        // 外部要求收起，但内部还在展开状态
        console.log('PersonaAnimation - 外部触发收起（反向展开）');

        // 使用与handleCollapse相同的反向展开动画
        pulseWave3.value = withTiming(0, {
          duration: 500,
          easing: Easing.out(Easing.quad)
        });

        pulseWave2.value = withDelay(100, withTiming(0, {
          duration: 500,
          easing: Easing.out(Easing.quad)
        }));

        pulseWave1.value = withDelay(200, withTiming(0, {
          duration: 500,
          easing: Easing.out(Easing.quad)
        }));

        fadeAnim.value = withDelay(150, withTiming(0, {
          duration: 600,
          easing: Easing.out(Easing.quad)
        }));

        expandAnim.value = withDelay(300, withTiming(0, {
          duration: 600,
          easing: Easing.out(Easing.quad)
        }, (finished) => {
          if (finished) {
            // 旋转偏移量已移除
            runOnJS(setInternalIsExpanded)(false);
          }
        }));
      }
    }
  }, [externalIsExpanded]);

  // 组件卸载时清理动画
  useEffect(() => {
    return () => {
      try {
        // 取消所有正在进行的动画
        cancelAnimation(expandAnim);
        cancelAnimation(fadeAnim);
        cancelAnimation(pulseWave1);
        cancelAnimation(pulseWave2);
        cancelAnimation(pulseWave3);
        cancelAnimation(rotate);
      } catch (error) {
        console.error('清理动画错误:', error);
      }
    };
  }, []);

  // 处理中心圆圈点击
  const handleCenterPress = () => {
    console.log('中心按钮被点击, 当前状态:', isExpanded);
    if (isExpanded) {
      handleCollapse();
    } else {
      handleExpand();
    }
  };

  // reanimated 动画样式
  const rotateStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotate.value}deg` }],
    };
  });

  // 平滑的标签反向旋转 - 避免收起时的突然跳转
  const counterRotateStyle = useAnimatedStyle(() => {
    // 使用平滑插值，确保旋转强度随展开程度线性变化
    // 这样收起时标签会平滑地从反向旋转回到正向
    const rotationStrength = interpolate(
      expandAnim.value,
      [0, 1],
      [0, 1]
    );

    // 应用平滑的反向旋转
    const counterRotation = -rotate.value * rotationStrength;

    return {
      transform: [{ rotate: `${counterRotation}deg` }],
    };
  });

  // 脉冲环样式
  const pulseRingStyle = useAnimatedStyle(() => {
    const normalizedRotate = rotate.value % 360;
    const opacity = interpolate(
      normalizedRotate,
      [0, 180, 360],
      [0.3, 0.6, 0.3]
    );
    const scale = interpolate(
      normalizedRotate,
      [0, 360],
      [1, 1.2]
    );
    
    return {
      opacity,
      transform: [{ scale }],
    };
  });

  // 展开动画样式
  const expandStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      expandAnim.value,
      [0, 0.5, 1],
      [0, 0.8, 1]
    );
    const opacity = interpolate(
      expandAnim.value,
      [0, 0.3, 1],
      [0, 0.5, 1]
    );
    
    return {
      transform: [{ scale }],
      opacity,
    };
  });

  // fadeStyle已整合到波浪动画样式中

  // 预定义三个波浪层的动画样式
  const wave1AnimationStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      pulseWave1.value,
      [0, 1],
      [0.3, 1] // 从30%缩放到100%
    );

    // 简化透明度计算：直接使用波浪值作为透明度
    const finalOpacity = pulseWave1.value;

    return {
      transform: [{ scale }],
      opacity: finalOpacity,
    };
  });

  const wave2AnimationStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      pulseWave2.value,
      [0, 1],
      [0.3, 1]
    );

    const finalOpacity = pulseWave2.value;

    return {
      transform: [{ scale }],
      opacity: finalOpacity,
    };
  });

  const wave3AnimationStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      pulseWave3.value,
      [0, 1],
      [0.3, 1]
    );

    const finalOpacity = pulseWave3.value;

    return {
      transform: [{ scale }],
      opacity: finalOpacity,
    };
  });

  // 获取对应波浪层的动画样式
  const getWaveAnimationStyle = (waveIndex) => {
    switch (waveIndex) {
      case 0:
        return wave1AnimationStyle;
      case 1:
        return wave2AnimationStyle;
      case 2:
        return wave3AnimationStyle;
      default:
        return wave1AnimationStyle;
    }
  };

  return (
    <View style={styles.container}>

      {/* 虚线圆圈背景 - 始终渲染，通过动画控制可见性 */}
      <Animated.View
        style={[
          styles.dashedCircle,
          expandStyle,
        ]}
      />

      {/* 旋转的标签容器 - 始终渲染，通过动画控制可见性 */}
      <Animated.View
        style={[
          styles.tagsContainer,
          rotateStyle,
          expandStyle,
        ]}
      >
          {/* 使用算法精确定位的标签圆圈 - 分层动画 */}
          {tags.map((tag, index) => {
            const position = calculateTagPosition(tag.angle);
            // 根据索引分配到不同的波浪层
            const waveIndex = index % 3; // 0, 1, 2 循环分配
            const tagAnimationStyle = getWaveAnimationStyle(waveIndex);

            return (
              <Animated.View
                key={index}
                style={[
                  styles.tagCircle,
                  counterRotateStyle, // 应用反向旋转保持文字正向
                  tagAnimationStyle, // 应用分层动画
                  {
                    left: position.left,
                    top: position.top,
                  },
                ]}
              >
                <ThemedText style={styles.tagText}>{tag.text}</ThemedText>
              </Animated.View>
            );
          })}

          {/* 虚线小圆圈 - 分层动画 */}
          {smallCircles.map((circle, index) => {
            const position = calculateSmallCirclePosition(circle.angle);
            // 小圆圈使用不同的波浪层
            const waveIndex = (index + 1) % 3; // 1, 2, 0 循环分配，与大标签错开
            const smallCircleAnimationStyle = getWaveAnimationStyle(waveIndex);

            return (
              <Animated.View
                key={`small-${index}`}
                style={[
                  styles.dashedSmallCircle,
                  counterRotateStyle, // 应用反向旋转保持文字正向
                  smallCircleAnimationStyle, // 应用分层动画
                  {
                    left: position.left,
                    top: position.top,
                  },
                ]}
              >
                <ThemedText style={styles.dashedCircleText}>{circle.text}</ThemedText>
              </Animated.View>
            );
          })}
        </Animated.View>

      {/* 中心头像 - 可点击，带点击效果 */}
      <TouchableOpacity
        onPress={handleCenterPress}
        activeOpacity={0.8}
        style={styles.centerAvatarTouchable}
      >
        <Animated.View style={styles.centerAvatar}>
          {avatarSource && <Image source={avatarSource} style={styles.avatarImage} />}
          {/* 添加点击提示效果 */}
          <View style={styles.clickIndicator}>
            <ThemedText style={styles.clickText}>
              {isExpanded ? '收起' : '展开'}
            </ThemedText>
          </View>
        </Animated.View>
      </TouchableOpacity>

      {/* 脉冲效果 - 提示可点击 */}
      {!isExpanded && (
        <Animated.View
          style={[
            styles.pulseRing,
            pulseRingStyle,
          ]}
        />
      )}

      {/* 手势提示文本 */}
      {/* <View style={styles.gestureHint}>
        <ThemedText style={styles.gestureHintText}>
          {isExpanded ? '👇 下滑收起' : '👆 上滑展开或点击头像'}
        </ThemedText>
      </View> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: 320,
    height: 320,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  gestureLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 320,
    height: 320,
    backgroundColor: 'transparent',
    zIndex: 1, // 设置为最低层，但仍能接收手势事件
  },
  dashedCircle: {
    position: 'absolute',
    width: 280,
    height: 280,
    borderRadius: 140,
    borderWidth: 2,
    borderColor: '#8B4513',
    borderStyle: 'dashed',
    top: 20, // 居中定位：(320-280)/2 = 20
    left: 20,
  },
  tagsContainer: {
    position: 'absolute',
    width: 320,
    height: 320,
    top: 0,
    left: 0,
  },
  tagCircle: {
    position: 'absolute',
    backgroundColor: '#FFFFFF',
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: '#8B4513',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tagText: {
    fontSize: 11,
    color: '#8B4513',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 14,
    paddingHorizontal: 4,
  },
  dashedSmallCircle: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#8B4513',
    borderStyle: 'dashed',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dashedCircleText: {
    fontSize: 10,
    color: '#8B4513',
    textAlign: 'center',
    fontWeight: '500',
  },
  centerAvatarTouchable: {
    position: 'absolute',
    top: 120, // 居中定位：(320-80)/2 = 120
    left: 120,
    width: 80,
    height: 80,
    zIndex: 1000, // 确保头像在手势层之上，能接收点击
  },
  centerAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FF8C00',
    overflow: 'visible', // 改为 visible 以显示点击提示
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  avatarImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
  },
  clickIndicator: {
    position: 'absolute',
    bottom: -30,
    left: '50%',
    transform: [{ translateX: -25 }], // 居中对齐
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    width: 50,
    alignItems: 'center',
  },
  clickText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  pulseRing: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#FF8C00',
    top: 110, // 居中定位：(320-100)/2 = 110
    left: 110,
    zIndex: 1, // 确保在中心圆圈下方
  },
  gestureHint: {
    position: 'absolute',
    bottom: -50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  gestureHintText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    overflow: 'hidden',
  },
});

export default PersonaAnimation;