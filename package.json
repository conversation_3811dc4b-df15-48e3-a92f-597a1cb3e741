{"name": "my-react-native-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "expo": "^53.0.20", "expo-blur": "^14.1.5", "expo-constants": "~17.1.7", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-status-bar": "~2.2.3", "matter-js": "^0.20.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-appwrite": "^0.10.0", "react-native-game-engine": "^1.2.0", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "typescript": "~5.8.3"}, "private": true, "proxy": "http://localhost:8080"}