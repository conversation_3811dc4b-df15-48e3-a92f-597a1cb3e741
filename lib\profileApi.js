// ...existing code...
// 带重试的请求封装


const requestWithRetry = async (endpoint, method = 'GET', data = null, retries = 3) => {
  try {
    return await request(endpoint, method, data);
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return requestWithRetry(endpoint, method, data, retries - 1);
    }
    throw error;
  }
};
// src/services/api.js
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
// 网络检测
const checkNetwork = async () => {
  const state = await NetInfo.fetch();
  if (!state.isConnected) {
    throw new Error('网络不可用，请检查连接');
  }
};

const API_BASE_URL = __DEV__
  ? 'https://dev-api.example.com'
  : 'https://prod-api.example.com';

// 通用请求封装
const request = async (endpoint, method = 'GET', data = null) => {
  await checkNetwork();
  const url = `${API_BASE_URL}/${endpoint}`;
  // const token = await AsyncStorage.getItem('authToken');

  const headers = {
    'Content-Type': 'application/json',
    // ...(token && { Authorization: `Bearer ${token}` }), // 注释掉token验证
  };

  const config = {
    method,
    headers,
    ...(data && { body: JSON.stringify(data) }),
  };

  try {
    const response = await fetchWithAuth(url, config);
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '请求失败');
    }

    return result;
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};

// 专用方法：保存用户画像
export const saveUserProfile = async (profileData) => {
  return request('user/profile', 'POST', profileData);
};

// 其他API方法...
export const fetchUserProfile = async () => {
  return request('user/profile', 'GET');
};