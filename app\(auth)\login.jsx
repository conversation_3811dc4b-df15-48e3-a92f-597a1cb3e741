import { StyleSheet, Text, Keyboard, TouchableWithoutFeedback, ActivityIndicator, View, TouchableOpacity, SafeAreaView, Image, ScrollView, Animated } from 'react-native'
import { Link, router } from 'expo-router'
import { useState, useEffect, useRef } from 'react'
import { useUser } from '../../hooks/useUser'
import { LinearGradient } from 'expo-linear-gradient'

import ThemedView from '../../components/ThemedView'
import ThemedText from '../../components/ThemedText'
import Spacer from '../../components/Spacer'
import ThemedTextInput from "../../components/ThemedTextInput"
import GradientGlassButton from '../../components/GradientGlassButton'
import CustomCheckbox from '../../components/CustomCheckbox'
import { Colors } from '../../constants/Colors'

const Login = () => {
  const [activeTab, setActiveTab] = useState('账号登录') // 新增：当前激活的选项卡
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [phone, setPhone] = useState("") // 新增：手机号
  const [verificationCode, setVerificationCode] = useState("") // 新增：验证码
  const [error, setError] = useState()
  const [showPassword, setShowPassword] = useState(false) // 新增：控制密码显示状态
  const [agreedToTerms, setAgreedToTerms] = useState(false) // 新增：用户协议同意状态

  // 动画相关状态
  const slideAnimation = useRef(new Animated.Value(1)).current // 0表示验证码登录，1表示账号登录，默认为1对应账号登录
  const fadeAnimation = useRef(new Animated.Value(1)).current

  const { user, login, profileSetupCompleted } = useUser()

  // 处理分页切换动画
  const handleTabChange = (newTab) => {
    if (newTab === activeTab) return

    // 淡出动画
    Animated.timing(fadeAnimation, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      // 切换状态
      setActiveTab(newTab)

      // 滑动动画
      Animated.timing(slideAnimation, {
        toValue: newTab === '账号登录' ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }).start()

      // 淡入动画
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start()
    })
  }

  const handleSubmit = async () => {
    setError(null)

    // 检查是否同意用户协议
    if (!agreedToTerms) {
      setError("请先阅读并同意《用户协议》和《隐私授权》")
      return
    }

    try {
      let result
      if (activeTab === '账号登录') {
        // 账号登录逻辑
        result = await login(email, password)
      } else {
        // 验证码登录逻辑（这里可以添加验证码登录的API调用）
        // 暂时使用邮箱登录作为示例
        result = await login(phone, verificationCode)
      }

      console.log('登录结果:', result)

      // 根据用户是否已完成人物画像设置决定跳转路径
      if (result.profileSetupCompleted) {
        console.log('用户已完成人物画像设置，直接跳转到计划页面')
        router.replace('/(dashboard)/plans')
      } else {
        console.log('用户未完成人物画像设置，跳转到人物画像页面')
        router.replace('/profile-setup')
      }
    } catch (error) {
      setError(error.message)
    }
  }

  // 获取验证码的处理函数
  const handleGetVerificationCode = () => {
    // 这里可以添加获取验证码的逻辑
    console.log('获取验证码:', phone)
  }

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <ThemedView style={styles.container} safe={false}>
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          {/* 返回按钮 */}
          <TouchableOpacity style={styles.backButton} onPress={() => router.push('/welcome')}>
            <Image source={require('../../assets/Arrows_left.png')} style={styles.backButtonIcon} />
          </TouchableOpacity>

          <Spacer height={85} />

          {/* 标题 */}
          <View style={styles.titleContainer}>
            <ThemedText title={true} style={styles.title}>
              欢迎登录
            </ThemedText>
            <ThemedText style={styles.subtitle}>
              我们等你好久了！
            </ThemedText>
          </View>

          <Spacer height={40} />

          {/* 分页选项卡 */}
          <LinearGradient
            colors={['#ffb87e', '#FFEEDB']}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={styles.tabContainer}
          >
            {/* 滑动的白色背景 */}
            <Animated.View
              style={[
                styles.slidingBackground,
                {
                  transform: [{
                    translateX: slideAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'], // 从0%滑动到100%
                    })
                  }]
                }
              ]}
            />

            <TouchableOpacity
              style={styles.tab}
              onPress={() => handleTabChange('验证码登录')}
            >
              <Text style={[styles.tabText, activeTab === '验证码登录' && styles.activeTabText]}>
                验证码登录
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.tab}
              onPress={() => handleTabChange('账号登录')}
            >
              <Text style={[styles.tabText, activeTab === '账号登录' && styles.activeTabText]}>
                账号登录
              </Text>
            </TouchableOpacity>
          </LinearGradient>

          {/* 白色容器 */}
          <View style={styles.whiteContainer}>
            <Spacer height={30} />

            {/* 根据选项卡显示不同的表单 */}
            <Animated.View style={{ opacity: fadeAnimation }}>
            {activeTab === '账号登录' ? (
              <View style={styles.formContainer}>
                <ThemedText style={styles.fieldLabel}>账号</ThemedText>
                <View style={styles.inputContainer}>
                  <Image source={require('../../assets/Auth_image/People_number.png')} style={styles.inputIcon} />
                  <ThemedTextInput
                    style={[styles.input, styles.inputWithIcon]}
                    placeholder="请输入账号"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                  />
                </View>

                <Spacer height={20} />
                <ThemedText style={styles.fieldLabel}>密码</ThemedText>
                <View style={styles.inputContainer}>
                  <Image source={require('../../assets/Auth_image/Lock.png')} style={styles.inputIcon} />
                  <ThemedTextInput
                    style={[styles.input, styles.passwordInput]}
                    placeholder="请输入密码"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Image
                      source={showPassword ? require('../../assets/Auth_image/Eyes_open.png') : require('../../assets/Auth_image/Eyes_close.png')}
                      style={styles.eyeIcon}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View style={styles.formContainer}>
                <ThemedText style={styles.fieldLabel}>手机号</ThemedText>
                <View style={styles.inputContainer}>
                  <Image source={require('../../assets/Auth_image/People_number.png')} style={styles.inputIcon} />
                  <ThemedTextInput
                    style={[styles.input, styles.inputWithIcon]}
                    placeholder="请输入手机号"
                    value={phone}
                    onChangeText={setPhone}
                    keyboardType="phone-pad"
                  />
                </View>

                <Spacer height={20} />
                <ThemedText style={styles.fieldLabel}>验证码</ThemedText>
                <View style={styles.inputContainer}>
                  <Image source={require('../../assets/Auth_image/Key.png')} style={styles.inputIcon} />
                  <ThemedTextInput
                    style={[styles.input, styles.inputWithIconAndButton]}
                    placeholder="请输入验证码"
                    value={verificationCode}
                    onChangeText={setVerificationCode}
                    keyboardType="number-pad"
                  />
                  <TouchableOpacity style={styles.getCodeButton} onPress={handleGetVerificationCode}>
                    <Text style={styles.getCodeText}>获取验证码</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
            </Animated.View>

            {/* 注册链接和忘记密码 */}
            <View style={styles.linksContainer}>
              <View style={styles.registerContainer}>
                <Text style={styles.registerText}>还没账户？</Text>
                <Link href="/register" replace>
                  <Text style={styles.registerLink}>去注册</Text>
                </Link>
              </View>
              {activeTab === '账号登录' && (
                <TouchableOpacity onPress={() => router.push('/(auth)/forgot-password')}>
                  <Text style={styles.forgotPasswordText}>忘记密码？</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* 登录按钮 */}
            <GradientGlassButton
              title="登录"
              onPress={handleSubmit}
              style={[styles.confirmButton, !agreedToTerms && styles.disabledButton]}
              disabled={!agreedToTerms}
            />

            {error && <Text style={styles.error}>{error}</Text>}

            {/* 用户协议 */}
            <View style={styles.agreementContainer}>
              <CustomCheckbox
                checked={agreedToTerms}
                onPress={() => setAgreedToTerms(!agreedToTerms)}
                checkedColor="#FFC885"
                size={15}
                style={styles.checkbox}
              />
              <Text style={styles.agreementText}>
                我已阅读并同意
                <Text style={styles.agreementLink}>《用户协议》</Text>
                和
                <Text style={styles.agreementLink}>《隐私授权》</Text>
              </Text>
            </View>

            {/* 第三方登录 */}
            <View style={styles.thirdPartyContainer}>
              <View style={styles.thirdPartyTitleContainer}>
                <View style={styles.thirdPartyLine} />
                <Text style={styles.thirdPartyTitle}>第三方账号登录</Text>
                <View style={styles.thirdPartyLine} />
              </View>
              <View style={styles.thirdPartyButtons}>
                <TouchableOpacity style={styles.thirdPartyButton}>
                  <Image source={require('../../assets/Auth_image/QQ.png')} style={styles.thirdPartyIcon} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.thirdPartyButton}>
                  <Image source={require('../../assets/Auth_image/Wechat.png')} style={styles.forthPartyIcon} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </ThemedView>
    </TouchableWithoutFeedback>
  )
}

export default Login

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    minHeight: '100%', // 确保内容至少铺满整个屏幕
    paddingHorizontal: 10,
    paddingTop: 20,
  },
  backButton: {
    position: 'absolute',
    top: 35,
    left: 20,
    zIndex: 1,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonIcon: {
    width: 24,
    height: 24,
  },
  whiteContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    borderBottomLeftRadius: 36,
    borderBottomRightRadius: 36,
    paddingHorizontal: 20,
    paddingVertical: 30,
    marginHorizontal: 16,
    marginTop: 0,
    paddingTop: 50,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  titleContainer: {
    alignItems: 'flex-start', // 左对齐
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    color: Colors.boldText, // 使用加粗字体颜色常量
  },
  subtitle: {
    fontSize: 16,
    color: Colors.boldText, // 使用正常字体颜色
    marginBottom: 20,
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: -55,
    zIndex: 1,
    height: 60,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    paddingTop: 0,
    paddingBottom: 0,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    backgroundColor: 'transparent',
    zIndex: 2,
  },
  slidingBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '50%',
    height: '100%',
    backgroundColor: 'white',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    zIndex: 1,
  },
  tabText: {
    color: 'black',
    fontWeight: '500',
    fontSize: 16,
  },
  activeTabText: {
    color: '#7A3C10',
    fontWeight: '500',
    fontSize: 16,
  },
  formContainer: {
    paddingHorizontal: 0, // 移除内边距，因为白色容器已有
  },
  fieldLabel: {
    fontSize: 16,
    color: Colors.boldText, // 使用加粗字体颜色常量
    marginBottom: 8,
    fontWeight: '500',
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    width: 20,
    height: 20,
    zIndex: 1,
  },
  input: {
    backgroundColor: '#F5F5F5',
    borderRadius: 36,
    paddingHorizontal: 20,
    paddingVertical: 15,
    fontSize: 16,
    color: Colors.normalText,
    padding: 0, // 重置ThemedTextInput的默认padding
  },
  inputWithIcon: {
    paddingLeft: 45,
    paddingRight: 20,
    paddingVertical: 15,
    flex: 1,
  },
  inputWithIconAndButton: {
    paddingLeft: 45,
    paddingRight: 80,
    paddingVertical: 15,
    flex: 1,
  },
  passwordInput: {
    paddingLeft: 45,
    paddingRight: 56,
    paddingVertical: 15,
    flex: 1,
  },
  eyeButton: {
    position: 'absolute',
    right: 20,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  eyeIcon: {
    width: 20,
    height: 20,
  },
  getCodeButton: {
    position: 'absolute',
    right: 15,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  getCodeText: {
    color: '#7A3C10',
    fontSize: 12,
    fontWeight: '600',
  },
  linksContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  registerText: {
    fontSize: 14,
    color: Colors.normalText, // 使用正常字体颜色
  },
  forgotPasswordText: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '500',
  },
  confirmButton: {
    marginBottom: 20,
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 0,
    marginBottom: 40,
  },
  checkbox: {
    marginRight: 12,
  },
  disabledButton: {
    opacity: 0.5,
  },
  agreementText: {
    fontSize: 12,
    color: Colors.normalText,
    flex: 1,
  },
  agreementLink: {
    color: '#7A3C10',
    fontSize: 12,
  },
  thirdPartyContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 0, // 移除底部边距，因为在白色容器内
  },
  thirdPartyTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  thirdPartyLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 15,
  },
  thirdPartyTitle: {
    fontSize: 14,
    color: Colors.normalText,
    paddingHorizontal: 10,
  },
  thirdPartyButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  thirdPartyButton: {
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: Colors.button, // 使用按钮颜色常量
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 33,
  },
  thirdPartyIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  forthPartyIcon: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  registerLinkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  registerPrompt: {
    fontSize: 14,
    color: Colors.normalText,
    marginRight: 8,
  },
  registerLink: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '600',
  },
  error: {
    color: Colors.warning,
    padding: 10,
    backgroundColor: Colors.lightOrange, // 使用Colors.js中的浅橙色作为错误背景
    borderColor: Colors.warning,
    borderWidth: 1,
    borderRadius: 6,
    marginHorizontal: 10,
    textAlign: 'center',
  }
})