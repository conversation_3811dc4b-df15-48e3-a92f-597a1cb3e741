import { StyleSheet, View, TouchableOpacity } from 'react-native'
import { useUser } from '../hooks/useUser'
import ThemedText from "../components/ThemedText"
import ThemedView from "../components/ThemedView"
import GradientGlassButton from '../components/GradientGlassButton'
import { Ionicons } from '@expo/vector-icons'
import { useRouter } from 'expo-router'

const groups = [
  [
    { label: '账号绑定', onPress: () => { } },
    { label: '个人信息设置', onPress: () => { } },
  ],
  [
    { label: '我的收藏', onPress: () => { } },
    { label: '隐私设置', onPress: () => { } },
    { label: '意见反馈', onPress: () => { } },
    { label: '工具与帮助', onPress: () => { } },
    { label: '夜间模式', onPress: () => { } },
    { label: '支付方式', onPress: () => { } },
  ],
]

const Settings = () => {

  const { logout } = useUser()
  const router = useRouter()

  return (
    <ThemedView style={styles.container}>
      {/* 顶部标题和返回 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backBtn}>
          <Ionicons name="chevron-back" size={28} color="#222" />
        </TouchableOpacity>
        <ThemedText style={styles.heading}>设置</ThemedText>
      </View>
      {/* 分组卡片 */}
      {groups.map((group, i) => (
        <View key={i} style={styles.groupCard}>
          {group.map((item, j) => (
            <TouchableOpacity key={item.label} style={styles.groupItem} onPress={item.onPress}>
              <ThemedText style={styles.groupLabel}>{item.label}</ThemedText>
              <Ionicons name="chevron-forward" size={22} color="#bbb" />
              {j !== group.length - 1 && <View style={styles.divider} />}
            </TouchableOpacity>
          ))}
        </View>
      ))}
      {/* 登出按钮 */}
      <GradientGlassButton
        onPress={logout}
        borderRadius={16}
        style={styles.logoutButton}
        gradientColors={['#FFB87E', '#FFB87E']}
        borderColor="rgba(255, 184, 126, 0.5)"
        blurBackgroundColor="rgba(255, 184, 126, 0.3)"
        textColor="#FFFFFF"
      >
        <View style={styles.logoutContent}>
          <Ionicons name="log-out-outline" size={24} color="#000000" style={{ marginRight: 8 }} />
          <ThemedText style={styles.logoutText}>退出登录</ThemedText>
        </View>
      </GradientGlassButton>
    </ThemedView>
  )
}

export default Settings

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FCF1E4',
    paddingHorizontal: 16,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    position: 'relative',
  },
  backBtn: {
    position: 'absolute',
    left: 0,
    top: -4,
    padding: 4,
    zIndex: 2,
  },
  heading: {
    fontWeight: 'bold',
    fontSize: 22,
    color: '#222',
    textAlign: 'center',
  },
  groupCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 18,
    paddingHorizontal: 0,
    paddingVertical: 0,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  groupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 28,
    paddingRight: 18,
    paddingVertical: 20,
    backgroundColor: 'transparent',
    position: 'relative',
  },
  groupLabel: {
    fontSize: 17,
    color: '#222',
    flex: 1,
  },
  divider: {
    position: 'absolute',
    left: 28,
    right: 18,
    bottom: 0,
    height: 1,
    backgroundColor: '#D0D0D0',
  },
  logoutButton: {
    marginTop: 32,
    marginBottom: 24,
    height: 56,
    shadowColor: '#FFB87E',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  logoutContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: 'bold',
  },
})
