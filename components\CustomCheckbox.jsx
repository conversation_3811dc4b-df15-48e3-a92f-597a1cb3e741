import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';

const CustomCheckbox = ({ 
  checked = false, 
  onPress, 
  size = 32,
  checkedColor = '#FFC885',
  uncheckedColor = '#e0e0e0',
  style 
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, { width: size, height: size }, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[
        styles.radioOuter,
        {
          width: size,
          height: size,
          backgroundColor: uncheckedColor,
        }
      ]}>
        <View style={[
          styles.radioInner,
          {
            backgroundColor: checked ? checkedColor : 'transparent',
            transform: [{ scale: checked ? 1.1 : 1 }],
          }
        ]} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioOuter: {
    borderRadius: 50,
    padding: 2,
    justifyContent: 'center',
    alignItems: 'center',
    // 模拟内阴影效果
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  radioInner: {
    width: '80%',
    height: '80%',
    borderRadius: 50,
    borderWidth: 1,
    borderColor: 'transparent',
  },
});

export default CustomCheckbox;
