import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

const GradientGlassButton = ({
  title,
  onPress,
  style,
  textStyle,
  disabled = false,
  gradientColors = ['#FFEEDB', '#ffb87e'],
  borderColor = 'rgba(255, 194, 121, 0.5)',
  blurBackgroundColor = 'rgba(255, 238, 219, 0.3)',
  textColor = '#7A3C10',
  borderRadius = 36,
  children
}) => {
  return (
    <TouchableOpacity
      style={[{
        width: '100%',
        height: 50,
        borderRadius: borderRadius,
        marginTop: 20,
        marginBottom: 20,
        overflow: 'hidden',
        position: 'relative',
      }, style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={{
          flex: 1,
          borderRadius: borderRadius,
          position: 'relative',
        }}
      >
        <View style={{
          flex: 1,
          borderRadius: borderRadius,
          borderWidth: 1,
          borderColor: borderColor,
          position: 'relative',
          overflow: 'hidden',
        }}>
          <LinearGradient
            colors={['rgba(255, 238, 219, 0.6)', 'rgba(255, 194, 121, 0.2)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: borderRadius,
            }}
          />
          <BlurView
            intensity={15}
            tint="light"
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: blurBackgroundColor,
              borderRadius: borderRadius,
            }}
          >
            {children || (
              <Text style={[{
                color: textColor,
                fontSize: 16,
                fontWeight: '400',
                textAlign: 'center',
              }, textStyle]}>
                {title}
              </Text>
            )}
          </BlurView>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default GradientGlassButton;
