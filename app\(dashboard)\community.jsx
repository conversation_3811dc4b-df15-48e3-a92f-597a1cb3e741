import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Animated, PanResponder, RefreshControl, Text, SafeAreaView } from 'react-native';
import Spacer from "../../components/Spacer";
import ThemedText from "../../components/ThemedText";
import ThemedView from "../../components/ThemedView";
import { Colors } from "../../constants/Colors";
import { useRouter, useLocalSearchParams } from 'expo-router';
import ProfileSidebar from "../../components/ProfileSidebar";
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';

// 在组件顶部引入图片
const likeIcon = require('../../assets/Like33.png');
const commentIcon = require('../../assets/Comments.png');
const forwardIcon = require('../../assets/Forward.png');
// 在顶部引入头像图片
const avatarJessica = require('../../assets/Community_image/AvatarOne.png');
const avatarKin = require('../../assets/Community_image/AvatarTwo.png');
const avatarCaaary = require('../../assets/Community_image/AcatarThree.png');
const photoJessica = require('../../assets/Community_image/PhotosOne.png');
const photoCaaary = require('../../assets/Community_image/Picture.png');

const Community = () => {
  const [activeTab, setActiveTab] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const router = useRouter();
  const params = useLocalSearchParams();

  const tabs = ['学习搭子', '自习室', '职业规划', '交友讨论'];

  // 从URL参数中获取tab参数
  const tab = params.tab;
  console.log('Search params:', params);
  console.log('Tab parameter:', tab);

  // 如果有tab参数，设置对应的活跃标签
  useEffect(() => {
    console.log('Tab parameter:', tab);
    console.log('Available tabs:', tabs);
    if (tab && tabs.includes(tab)) {
      console.log('Setting active tab to:', tab);
      setActiveTab(tab);
    }
  }, [tab, tabs]);

  // 标签与页面映射
  const tabToPage = {
    '学习搭子': '/community-team',
    '自习室': '/community-study',
    '职业规划': '/community-career',
    '交友讨论': '/community-friends',
  };

  const allPosts = {
    '交友讨论': [
      {
        id: 7,
        user: '团队队长',
        time: '三小时前',
        content: '寻找学习伙伴！一起准备考研，互相监督',
        likes: 67,
        comments: 12,
        hasMedia: false
      },
      {
        id: 8,
        user: '学习小组',
        time: '五小时前',
        content: '英语口语练习小组招新，每周线上练习',
        likes: 123,
        comments: 31,
        hasMedia: true,
        mediaType: 'single'
      },
      {
        id: 15,
        user: '编程小组',
        time: '六小时前',
        content: 'Python学习小组招新，零基础到进阶',
        likes: 89,
        comments: 24,
        hasMedia: false
      },
      {
        id: 16,
        user: '数学小组',
        time: '七小时前',
        content: '高等数学互助小组，一起攻克难题',
        likes: 145,
        comments: 38,
        hasMedia: true,
        mediaType: 'grid'
      },
      {
        id: 17,
        user: '考研联盟',
        time: '八小时前',
        content: '2024考研互助群，资源共享，经验交流',
        likes: 267,
        comments: 56,
        hasMedia: false
      }
    ],
    '自习室': [
      {
        id: 3,
        user: '喵喵喵',
        time: '一小时前',
        content: '今天在图书馆自习，效率很高！推荐大家也来试试',
        likes: 28,
        comments: 5,
        hasMedia: false
      },
      {
        id: 4,
        user: '学习达人',
        time: '两小时前',
        content: '分享一个高效学习方法：番茄工作法真的很有效',
        likes: 156,
        comments: 23,
        hasMedia: true,
        mediaType: 'single'
      },
      {
        id: 9,
        user: '考研党',
        time: '三小时前',
        content: '考研倒计时100天，每天坚持学习8小时，加油！',
        likes: 89,
        comments: 15,
        hasMedia: false
      },
      {
        id: 10,
        user: '夜猫子',
        time: '四小时前',
        content: '深夜学习效率最高，推荐大家试试夜读模式',
        likes: 67,
        comments: 12,
        hasMedia: true,
        mediaType: 'grid'
      },
      {
        id: 11,
        user: '学霸小王',
        time: '五小时前',
        content: '今天完成了数学复习，感觉进步很大！',
        likes: 134,
        comments: 28,
        hasMedia: false
      }
    ],
    '职业规划': [
      {
        id: 5,
        user: '职场导师',
        time: '四小时前',
        content: '应届毕业生如何选择第一份工作？我的建议是...',
        likes: 89,
        comments: 18,
        hasMedia: false
      },
      {
        id: 6,
        user: 'HR小姐姐',
        time: '昨天',
        content: '面试技巧分享：如何让HR对你印象深刻',
        likes: 234,
        comments: 45,
        hasMedia: true,
        mediaType: 'grid'
      },
      {
        id: 12,
        user: '产品经理',
        time: '六小时前',
        content: '产品经理的成长路径分享，从初级到高级的必经之路',
        likes: 156,
        comments: 32,
        hasMedia: false
      },
      {
        id: 13,
        user: '技术大牛',
        time: '七小时前',
        content: '程序员35岁危机？我的转型经验分享',
        likes: 298,
        comments: 67,
        hasMedia: true,
        mediaType: 'single'
      },
      {
        id: 14,
        user: '创业导师',
        time: '八小时前',
        content: '创业初期如何平衡工作与生活？',
        likes: 78,
        comments: 19,
        hasMedia: false
      }
    ],
    '学习搭子': [
      {
        id: 1,
        user: 'Jessica',
        time: '三小时前',
        content: 'Study or Sleep ?',
        likes: 92,
        comments: 11,
        hasMedia: true,
        mediaType: 'single'
      },
      {
        id: 2,
        user: 'Kin',
        time: '昨天',
        content: '<<爱因斯坦的梦>> 住在山上的人在过去,会有一天,科学家发现:...',
        likes: 46,
        comments: 12,
        hasMedia: true,
        mediaType: 'grid'
      },
      {
        id: 3,
        user: 'Caaary',
        time: '三小时前',
        content: '从方法论的角度来讨论外语!在学习的过程中你是如何调整心态的...',
        likes: 92,
        comments: 11,
        hasMedia: true,
        mediaType: 'single'
      }
    ]
  };

  // 根据当前选中的标签获取对应的帖子
  const currentPosts = allPosts[activeTab || tabs[0]] || [];

  // 下拉刷新处理函数
  const onRefresh = () => {
    setRefreshing(true);
    // 模拟网络请求延迟
    setTimeout(() => {
      setRefreshing(false);
      // 这里可以添加实际的数据刷新逻辑
      console.log('刷新完成');

      // 模拟添加新数据（实际项目中这里应该是从服务器获取新数据）
      const currentTab = activeTab || tabs[0];
      console.log(`刷新了 ${currentTab} 标签的数据`);

      // 根据不同标签显示不同的刷新提示
      const refreshMessages = {
        '学习搭子': '已更新最新的学习伙伴信息',
        '自习室': '已更新最新的自习动态',
        '职业规划': '已更新最新的职业资讯',
        '交友讨论': '已更新最新的讨论内容'
      };

      console.log(refreshMessages[currentTab] || '数据已更新');
    }, 2000);
  };

  const kinPictures = [
    require('../../assets/Community_image/PictureOne.png'),
    require('../../assets/Community_image/PictureTwo.png'),
    require('../../assets/Community_image/PictureThree.png'),
    require('../../assets/Community_image/PictureFour.png'),
    require('../../assets/Community_image/PictureFive.png'),
  ];

  return (
    <ThemedView style={styles.container} safe={false}>
      {/* 侧边栏 */}
      <ProfileSidebar visible={sidebarVisible} onClose={() => setSidebarVisible(false)} />

      {/* 整体可滚动的瀑布流 */}
      <ScrollView
        style={styles.postsContainer}
        contentContainerStyle={{ paddingBottom: 120 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#4A90E2']}
            tintColor="#4A90E2"
            title="正在刷新..."
            titleColor="#666"
            progressBackgroundColor="#f5f5f5"
          />
        }
      >
        {/* 顶部标题栏 - 现在在ScrollView内部 */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerIcon} onPress={() => setSidebarVisible(true)}>
            <Image source={require('../../assets/FrameThree.png')} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>社区</ThemedText>
          <TouchableOpacity style={styles.headerIcon} onPress={() => {
            console.log('Button clicked! Navigating to Chat page...');
            try {
              router.push('../Chat');
              console.log('Navigation attempted with ../Chat');
            } catch (error) {
              console.error('Navigation error:', error);
            }
          }}>
            <Image source={require('../../assets/FrameFour.png')} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
          </TouchableOpacity>
        </View>
        {/* 四个tab按钮 */}
        <View style={styles.tabContainer}>
          {tabs.map((tab, idx) => (
            <View key={tab} style={[styles.tab, activeTab === tab && styles.tabActive]}>
              <TouchableOpacity
                style={styles.tabTouchable}
                onPress={() => {
                  setActiveTab(tab);
                  const targetPage = tabToPage[tab];
                  if (targetPage) {
                    router.push(targetPage);
                  } else {
                    console.warn(`No page mapping found for tab: ${tab}`);
                  }
                }}
              >
                <ThemedText style={[styles.tabText, activeTab === tab && styles.tabTextActive]}>{tab}</ThemedText>
              </TouchableOpacity>
            </View>
          ))}
        </View>

        {/* 帖子卡片 */}
        {currentPosts.map((post) => (
          <View key={post.id} style={styles.post}>
            {/* 用户信息 */}
            <View style={styles.postHeader}>
              <View style={styles.userInfo}>
                {post.user === 'Jessica' ? (
                  <Image source={avatarJessica} style={styles.cardAvatar} />
                ) : post.user === 'Kin' ? (
                  <Image source={avatarKin} style={styles.cardAvatar} />
                ) : post.user === 'Caaary' ? (
                  <Image source={avatarCaaary} style={styles.cardAvatar} />
                ) : post.user === '喵喵喵' ? (
                  <Image source={require('../../assets/Community_image/AvatarSeven.png')} style={styles.cardAvatar} />
                ) : post.user === '学习达人' ? (
                  <Image source={require('../../assets/Community_image/AvatarFour.png')} style={styles.cardAvatar} />
                ) : post.user === '职场导师' ? (
                  <Image source={require('../../assets/Community_image/AvatarFive.png')} style={styles.cardAvatar} />
                ) : post.user === 'HR小姐姐' ? (
                  <Image source={require('../../assets/Community_image/AvatarSix.png')} style={styles.cardAvatar} />
                ) : post.user === '考研党' || post.user === '夜猫子' || post.user === '学霸小王' ? (
                  <Image source={require('../../assets/Community_image/AvatarOne.png')} style={styles.cardAvatar} />
                ) : post.user === '产品经理' || post.user === '技术大牛' || post.user === '创业导师' ? (
                  <Image source={require('../../assets/Community_image/AvatarTwo.png')} style={styles.cardAvatar} />
                ) : post.user === '编程小组' || post.user === '数学小组' || post.user === '考研联盟' ? (
                  <Image source={require('../../assets/Community_image/AcatarThree.png')} style={styles.cardAvatar} />
                ) : (
                  <View style={styles.cardAvatar} />
                )}
                <ThemedText style={styles.username}>{post.user}</ThemedText>
              </View>
              <View style={styles.postMeta}>
                <TouchableOpacity>
                  <ThemedText style={styles.arrowIcon}>›</ThemedText>
                </TouchableOpacity>
                <ThemedText style={styles.timeText}>{post.time}</ThemedText>
              </View>
            </View>
            {/* 帖子内容 */}
            <ThemedText style={styles.postContent}>{post.content}</ThemedText>
            {/* 媒体占位符 */}
            {post.hasMedia && (
              <View style={styles.mediaContainer}>
                {post.mediaType === 'single' ? (
                  post.user === 'Jessica' ? (
                    <Image source={photoJessica} style={styles.singleMedia} />
                  ) : post.user === 'Caaary' ? (
                    <Image source={photoCaaary} style={styles.singleMedia} />
                  ) : post.user === '学习达人' ? (
                    <Image source={require('../../assets/Community_image/Picture.png')} style={styles.singleMedia} />
                  ) : post.user === '技术大牛' ? (
                    <Image source={require('../../assets/Community_image/PhotosOne.png')} style={styles.singleMedia} />
                  ) : (
                    <View style={styles.singleMedia} />
                  )
                ) : (
                  post.user === 'Kin' ? (
                    <View style={[styles.gridMedia, { flexDirection: 'row', flexWrap: 'wrap' }]}>
                      {kinPictures.map((img, idx) => (
                        <Image key={idx} source={img} style={{ width: 80, height: 80, borderRadius: 8, margin: 2 }} />
                      ))}
                    </View>
                  ) : post.user === '夜猫子' || post.user === 'HR小姐姐' || post.user === '数学小组' ? (
                    <View style={[styles.gridMedia, { flexDirection: 'row', flexWrap: 'wrap' }]}>
                      {kinPictures.slice(0, 4).map((img, idx) => (
                        <Image key={idx} source={img} style={{ width: 80, height: 80, borderRadius: 8, margin: 2 }} />
                      ))}
                    </View>
                  ) : (
                    <View style={styles.gridMedia}>
                      {[1, 2, 3, 4, 5, 6].map((item) => (
                        <View key={item} style={styles.gridItem} />
                      ))}
                    </View>
                  )
                )}
              </View>
            )}
            {/* 互动按钮 */}
            <View style={styles.interactions}>
              <TouchableOpacity style={styles.interactionBtn}>
                <Image source={likeIcon} style={{ width: 20, height: 20, marginRight: 2 }} />
                <ThemedText style={styles.interactionCount}>{post.likes}</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.interactionBtn}>
                <Image source={commentIcon} style={{ width: 20, height: 20, marginRight: 2 }} />
                <ThemedText style={styles.interactionCount}>{post.comments}</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.interactionBtn}>
                <Image source={forwardIcon} style={{ width: 20, height: 20 }} />
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </ThemedView>
  );
};


const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#FFF8F3', paddingHorizontal: 16 },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 16,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerIcon: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuIcon: {
    width: 18,
    height: 2,
    backgroundColor: '#333',
    borderRadius: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  threeDots: {
    fontSize: 20,
    color: '#333',
    fontWeight: 'bold',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 0,
    paddingVertical: 0,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tab: {
    flex: 1,
    paddingHorizontal: 0,
    paddingVertical: 0,
    marginHorizontal: 4,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    justifyContent: 'center',
    height: 130,
    minWidth: 0,
    maxWidth: '100%',
  },
  tabTouchable: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabActive: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  tabTextActive: {
    color: '#fff',
  },
  postsContainer: {
    // flex: 1, // 不要flex:1，避免撑满父容器导致外边距失效
    paddingTop: 0,
    backgroundColor: 'transparent',
  },
  post: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#ddd',
    marginRight: 8,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  postMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowIcon: {
    fontSize: 16,
    color: '#999',
    marginRight: 8,
  },
  timeText: {
    fontSize: 12,
    color: '#999',
  },
  postContent: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginBottom: 12,
  },
  mediaContainer: {
    marginBottom: 12,
  },
  singleMedia: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  gridMedia: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  gridItem: {
    width: '32%',
    aspectRatio: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
  },
  interactions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  interactionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  interactionIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  interactionCount: {
    fontSize: 12,
    color: '#666',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
  },
  sidebar: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '80%',
    height: '100%',
    paddingTop: 60,
    paddingHorizontal: 16,
    overflow: 'hidden',
  },
  gradientOverlay: {
    flex: 1,
    paddingTop: 60,
    paddingHorizontal: 16,
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    padding: 16,
    alignItems: 'center',
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#ddd',
    marginRight: 8,
  },
  name: {
    fontSize: 20,
    fontWeight: '600',
    color: '#222',
  },
  tags: {
    flexDirection: 'row',
    marginTop: 8,
  },
  tag: {
    backgroundColor: '#ccc',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    fontSize: 12,
    marginHorizontal: 4,
    color: '#666',
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
  },
  statLabel: {
    fontSize: 12,
    color: '#555',
  },
  menu: {
    flex: 1,
  },
  menuItem: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: '#FFB87E',
    borderBottomWidth: 0,
  },
  menuText: {
    fontSize: 16,
    color: '#222',
  },
  cardAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#ddd',
    marginRight: 8,
  },
});

export default Community;