import { StyleSheet, View, Image } from 'react-native'
import { router } from 'expo-router'
import { Colors } from '../constants/Colors'
import ThemedView from '../components/ThemedView'
import ThemedText from '../components/ThemedText'
import Spacer from '../components/Spacer'
import GradientGlassButton from '../components/GradientGlassButton'

const Welcome = () => {
  const handleLogin = () => {
    router.push('/(auth)/login')
  }

  const handleGuest = () => {
    // 游客模式直接进入主页
    router.replace('/(dashboard)/plans?tab=plans')
  }

  return (
    <ThemedView style={styles.container} safe={true}>
      <Spacer height={100} />

      {/* 卡通形象 */}
      <View style={styles.characterContainer}>
        <Image
          source={require('../assets/standdog.png')}
          style={styles.characterImage}
          resizeMode="contain"
        />
      </View>

      <Spacer height={60} />

      {/* APP名称 */}
      <ThemedText style={styles.appName}>
        APP名称
      </ThemedText>

      <Spacer height={120} />

      {/* 登录按钮 */}
      <GradientGlassButton
        title="登录"
        onPress={handleLogin}
        style={styles.loginButton}
      />

      <Spacer height={20} />

      {/* 游客按钮 */}
      <GradientGlassButton
        title="游客"
        onPress={handleGuest}
        style={styles.guestButton}
        gradientColors={['#FFE7CE', '#FDAA6C']}
        borderColor="rgba(255, 231, 206, 0.5)"
        blurBackgroundColor="rgba(255, 231, 206, 0.3)"
      />

      <Spacer height={60} />
    </ThemedView>
  )
}

export default Welcome

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: Colors.background, // #FFEEDB
  },
  characterContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  characterImage: {
    width: 200,
    height: 200,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.boldText, // #7A3C10
    textAlign: 'center',
  },
  loginButton: {
    width: '80%',
    marginTop: 0,
    marginBottom: 0,
  },
  guestButton: {
    width: '80%',
    marginTop: 0,
    marginBottom: 0,
  },
})
