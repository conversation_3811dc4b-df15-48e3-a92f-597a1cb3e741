import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../components/ThemedText';
import ThemedView from '../components/ThemedView';
import { Colors } from '../constants/Colors';

const Chat = () => {
  const router = useRouter();

  const chatData = [
    {
      id: 1,
      name: '咕咕咕马冬梅',
      message: '快来和我一起参加知识竞赛',
      time: '10小时前',
      avatar: require('../assets/Community_image/AvatarOne.png')
    },
    {
      id: 2,
      name: '三角初平方',
      message: '快来和我一起探索世界',
      time: '05-14',
      avatar: require('../assets/Community_image/AvatarTwo.png')
    },
    {
      id: 3,
      name: '一颗鸡蛋黄',
      message: '',
      time: '05-14',
      avatar: require('../assets/Community_image/AvatarThree.png')
    },
    {
      id: 4,
      name: '设计师深海',
      message: '',
      time: '05-14',
      avatar: require('../assets/Community_image/AvatarFour.png')
    }
  ];

  return (

    <ThemedView style={styles.container}>

      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => {
          console.log('Navigating back to community');
          if (router.canGoBack()) {
            router.back();
          } else {
            router.push('/(dashboard)/community');
          }
        }}>
          <Image source={require('../assets/FrameTwo.png')} style={styles.backIcon} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>消息列表</ThemedText>
        <TouchableOpacity>
          <Image source={require('../assets/FrameFour.png')} style={styles.settingsIcon} />
        </TouchableOpacity>
      </View>

      {/* 功能按钮区 */}
      <View style={styles.functionRow}>
        <TouchableOpacity style={styles.functionButton} onPress={() => {
          console.log('Navigating to AllChats...');
          router.push('/Chats/AllChats');
        }}>
          <View style={styles.functionIcon}>
            <Image source={require('../assets/favicon.png')} style={styles.iconImage} />
          </View>
          <ThemedText style={styles.functionText}>所有评论</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.functionButton}
          onPress={() => {
            console.log('Navigating to MyLike...');
            try {
              router.push('/Chats/MyLike');
            } catch (error) {
              console.error('Navigation error:', error);
              // 备用路径
              router.push('/(dashboard)/community');
            }
          }}
        >
          <View style={styles.functionIcon}>
            <Image source={require('../assets/Like33.png')} style={styles.iconImage} />
          </View>
          <ThemedText style={styles.functionText}>我的点赞</ThemedText>
        </TouchableOpacity>




        <TouchableOpacity
          style={styles.functionButton}
          onPress={() => {
            console.log('关注功能暂未开放');
            // 这里可以添加关注页面的导航或者显示提示
          }}
        >
          <View style={styles.functionIcon}>
            <Image source={require('../assets/favicon.png')} style={styles.iconImage} />
          </View>
          <ThemedText style={styles.functionText}>关注</ThemedText>
        </TouchableOpacity>


        <TouchableOpacity
          style={styles.functionButton}
          onPress={() => {
            console.log('Navigating to SytemMessage...');
            try {
              router.push('/Chats/SytemMessage');
            } catch (error) {
              console.error('Navigation error:', error);
              // 备用路径
              router.push('/(dashboard)/community');
            }
          }}
        >
          <View style={styles.functionIcon}>
            <Image source={require('../assets/favicon.png')} style={styles.iconImage} />
          </View>
          <ThemedText style={styles.functionText}>系统消息</ThemedText>
        </TouchableOpacity>
      </View>

      {/* 聊天列表 */}
      <View style={styles.chatList}>
        {chatData.map((chat) => (
          <TouchableOpacity key={chat.id} style={styles.chatItem}>
            <Image source={chat.avatar} style={styles.avatar} />
            <View style={styles.chatContent}>
              <View style={styles.chatHeader}>
                <ThemedText style={styles.chatName}>{chat.name}</ThemedText>
                <ThemedText style={styles.chatTime}>{chat.time}</ThemedText>
              </View>
              {chat.message ? (
                <ThemedText style={styles.chatMessage}>{chat.message}</ThemedText>
              ) : null}
            </View>
          </TouchableOpacity>
        ))}
      </View>

    </ThemedView>

  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#fff',
  },
  backIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  settingsIcon: {
    marginTop: 15,
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  functionRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  functionButton: {
    alignItems: 'center',
    flex: 1,
  },
  functionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f5f5f5',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  iconImage: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  functionText: {
    fontSize: 12,
    color: Colors.light.text,
    textAlign: 'center',
  },
  chatList: {
    flex: 1,
    backgroundColor: '#fff',
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  chatTime: {
    fontSize: 12,
    color: '#999',
  },
  chatMessage: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
});

export default Chat;
