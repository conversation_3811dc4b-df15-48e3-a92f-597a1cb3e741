import { useLocal<PERSON>earch<PERSON>ara<PERSON>, useRouter } from "expo-router"
import { useColorScheme, View, TouchableOpacity, Image, StyleSheet, Dimensions } from "react-native"
import { useEffect, useState, useRef, useCallback } from "react"
import { Colors } from "../../constants/Colors"
import { useUser } from "../../hooks/useUser"
import AsyncStorage from '@react-native-async-storage/async-storage'
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS
} from 'react-native-reanimated'

import UserOnly from "../../components/auth/UserOnly"
import ThemedText from "../../components/ThemedText"

// 导入页面组件
import IndexPage from "./index"
import PlansPage from "./plans"
import CommunityPage from "./community"
import ProfilePage from "./profile"

const { width: screenWidth } = Dimensions.get('window')

// 定义颜色主题
const tabColors = {
  index: { bg: '#e3f2fd', slider: '#CDEAFA' },
  plans: { bg: '#e8f5e9', slider: '#E2DCF1' },
  more: { bg: '#fff3e0', slider: '#FF8D00' },
  community: { bg: '#f3e5f5', slider: '#DBF1CD' },
  profile: { bg: '#ffebee', slider: '#FFE2CD' }
};

export default function DashboardLayout() {
  const colorScheme = useColorScheme()
  const theme = Colors[colorScheme] ?? Colors.light
  const { tab } = useLocalSearchParams()
  const { user, authChecked } = useUser()
  const router = useRouter()
  const [ready, setReady] = useState(false)
  const [activeTab, setActiveTab] = useState('plans')
  const [showMorePanel, setShowMorePanel] = useState(false)

  // 动画值
  const tabBarContentWidth = screenWidth - 40;
  const actualTabWidth = tabBarContentWidth / 5;
  const sliderTranslateX = useSharedValue(actualTabWidth * 1)
  const sliderScale = useSharedValue(1)
  const sliderColor = useSharedValue(tabColors.plans.slider)
  const iconScales = useRef([
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1)
  ]).current

  // 为每个图标创建动画样式
  const iconAnimatedStyles = [
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[0].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[1].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[2].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[3].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[4].value }],
    })),
  ];

  // 为每个图标的未选中状态创建动画样式
  const iconInactiveStyles = [
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'index' ? 0 : 1, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'index' ? 0.7 : 1, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'index' ? '180deg' : '0deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'plans' ? 0 : 1, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'plans' ? 0.7 : 1, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'plans' ? '180deg' : '0deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(showMorePanel ? 0 : 1, { duration: 250 }),
      transform: [
        { scale: withTiming(showMorePanel ? 0.7 : 1, { duration: 250 }) },
        { rotate: withTiming(showMorePanel ? '180deg' : '0deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'community' ? 0 : 1, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'community' ? 0.7 : 1, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'community' ? '180deg' : '0deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'profile' ? 0 : 1, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'profile' ? 0.7 : 1, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'profile' ? '180deg' : '0deg', { duration: 250 }) }
      ]
    })),
  ];

  // 为每个图标的选中状态创建动画样式
  const iconActiveStyles = [
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'index' ? 1 : 0, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'index' ? 1 : 1.3, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'index' ? '0deg' : '-180deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'plans' ? 1 : 0, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'plans' ? 1 : 1.3, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'plans' ? '0deg' : '-180deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(showMorePanel ? 1 : 0, { duration: 250 }),
      transform: [
        { scale: withTiming(showMorePanel ? 1 : 1.3, { duration: 250 }) },
        { rotate: withTiming(showMorePanel ? '0deg' : '-180deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'community' ? 1 : 0, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'community' ? 1 : 1.3, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'community' ? '0deg' : '-180deg', { duration: 250 }) }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(activeTab === 'profile' ? 1 : 0, { duration: 250 }),
      transform: [
        { scale: withTiming(activeTab === 'profile' ? 1 : 1.3, { duration: 250 }) },
        { rotate: withTiming(activeTab === 'profile' ? '0deg' : '-180deg', { duration: 250 }) }
      ]
    })),
  ];

  // more 面板的动画样式
  const morePanelAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(showMorePanel ? 1 : 0, { duration: 250 }),
      transform: [
        {
          translateY: withTiming(showMorePanel ? 0 : 10, {
            duration: 250
          })
        },
        {
          scale: withTiming(showMorePanel ? 1 : 0.98, {
            duration: 300
          })
        }
      ]
    };
  });

  // 更新滑块位置的函数
  const updateSliderPosition = useCallback((tabName) => {
    const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
    const tabIndex = tabIndexMap[tabName] || 1;
    const tabBarContentWidth = screenWidth - 40;
    const actualTabWidth = tabBarContentWidth / 5;
    const targetPosition = actualTabWidth * tabIndex;
    sliderTranslateX.value = withSpring(targetPosition, { damping: 15, stiffness: 150 });
    sliderColor.value = withTiming(tabColors[tabName]?.slider || tabColors.plans.slider, { duration: 300 });
  }, [sliderTranslateX, sliderColor, screenWidth]);

  // 监听activeTab变化，同步更新滑块位置
  useEffect(() => {
    if (ready && activeTab) {
      if (tab && tab !== activeTab) {
        updateSliderPosition(activeTab);
      }
    }
  }, [activeTab, ready, updateSliderPosition, tab]);

  useEffect(() => {
    const checkAndRedirect = async () => {
      if (authChecked && user) {
        const userProfileKey = `profileSetupCompleted_${user.userAccount}`;
        const setupCompleted = await AsyncStorage.getItem(userProfileKey);
        const isCompleted = setupCompleted === "true";
        const redirected = await AsyncStorage.getItem("dashboardRedirectedOnce");
        if (!isCompleted) {
          setReady(false);
          await AsyncStorage.removeItem("dashboardRedirectedOnce");
          await router.replace('/profile-setup');
          return;
        }
        if (!tab && isCompleted && redirected !== "true") {
          await AsyncStorage.setItem("dashboardRedirectedOnce", "true");
          setReady(false);
          await router.replace('/(dashboard)/plans?tab=plans');
          return;
        }
        if (tab === "plans" || tab === "index" || tab === "community" || tab === "profile") {
          setReady(true);
          setActiveTab(tab);
        } else {
          setReady(true);
        }
      }
    };
    checkAndRedirect();
  }, [authChecked, user, tab, router]);

  // handleTabPress逻辑 - 修复版本
  const handleTabPress = useCallback((tabName) => {
    if (tabName === 'more') {
      if (!showMorePanel) {
        setTimeout(() => setShowMorePanel(true), 300);
      } else {
        setShowMorePanel(false);
      }

      // 确保滑块移动到 more 位置
      const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
      const tabIndex = tabIndexMap['more']; // 固定为2
      const tabBarContentWidth = screenWidth - 40;
      const actualTabWidth = tabBarContentWidth / 5;
      const targetPosition = actualTabWidth * tabIndex;

      sliderTranslateX.value = withSpring(targetPosition, {
        damping: 18,
        stiffness: 180,
        mass: 0.7
      });
      sliderColor.value = withTiming(tabColors.more.slider, {
        duration: 300
      });

      // more 图标的缩放动画
      iconScales.forEach((scale, index) => {
        if (index === tabIndex) {
          scale.value = withSpring(1.15, {
            damping: 12,
            stiffness: 280,
            mass: 0.5
          }, () => {
            scale.value = withSpring(1, {
              damping: 12,
              stiffness: 280,
              mass: 0.5
            });
          });
        } else {
          scale.value = withSpring(1, {
            damping: 12,
            stiffness: 280,
            mass: 0.5
          });
        }
      });

      return;
    }

    if (showMorePanel) setShowMorePanel(false);

    const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
    const tabIndex = tabIndexMap[tabName];

    setActiveTab(tabName);

    // 同步更新滑块位置和颜色
    const tabBarContentWidth = screenWidth - 40;
    const actualTabWidth = tabBarContentWidth / 5;
    const targetPosition = actualTabWidth * tabIndex;

    sliderTranslateX.value = withSpring(targetPosition, {
      damping: 18,
      stiffness: 180,
      mass: 0.7
    });
    sliderColor.value = withTiming(tabColors[tabName]?.slider || tabColors.plans.slider, {
      duration: 300
    });

    // 图标缩放动画
    iconScales.forEach((scale, index) => {
      if (index === tabIndex) {
        scale.value = withSpring(1.15, {
          damping: 12,
          stiffness: 280,
          mass: 0.5
        }, () => {
          scale.value = withSpring(1, {
            damping: 12,
            stiffness: 280,
            mass: 0.5
          });
        });
      } else {
        scale.value = withSpring(1, {
          damping: 12,
          stiffness: 280,
          mass: 0.5
        });
      }
    });

    // 更新URL参数
    router.setParams({ tab: tabName });
  }, [router, iconScales, activeTab, sliderTranslateX, sliderColor, screenWidth, showMorePanel,
    setShowMorePanel]);

  const sliderAnimatedStyle = useAnimatedStyle(() => {
    const tabBarContentWidth = screenWidth - 40;
    const actualTabWidth = tabBarContentWidth / 5;
    const sliderSize = 60;
    // 让滑块居中于当前tab
    const centerX = sliderTranslateX.value + actualTabWidth / 2 - sliderSize / 2;
    return {
      transform: [
        { translateX: centerX },
        { scale: sliderScale.value }
      ],
      backgroundColor: sliderColor.value,
      width: sliderSize,
      height: sliderSize,
      borderRadius: sliderSize / 2,
      position: 'absolute',
      top: 15,
      zIndex: 0,
      alignSelf: 'flex-start',
    };
  });

  const tabItems = [
    {
      name: 'index',
      title: '主页',
      icon: require('../../assets/Tabbar/Index_Study_0.png'),
      iconActive: require('../../assets/Tabbar/Index_study_1.png')
    },
    {
      name: 'plans',
      title: '计划',
      icon: require('../../assets/Tabbar/Plan_crown.png'),
      iconActive: require('../../assets/Tabbar/Plan_crown_1.png')
    },
    {
      name: 'more',
      title: '更多',
      icon: require('../../assets/Tabbar/More_0.png'),
      iconActive: require('../../assets/Tabbar/More_1.png')
    },
    {
      name: 'community',
      title: '社区',
      icon: require('../../assets/Tabbar/Community_dog_scratch_0.png'),
      iconActive: require('../../assets/Tabbar/Community_dog_scratch_1.png')
    },
    {
      name: 'profile',
      title: '我的',
      icon: require('../../assets/Tabbar/Mine_smiling_0.png'),
      iconActive: require('../../assets/Tabbar/Mine_smiling_1.png')
    }
  ];

  if (!ready) {
    return null;
  }

  // 渲染当前页面
  const renderCurrentPage = () => {
    switch (activeTab) {
      case 'index':
        return <IndexPage />;
      case 'plans':
        return <PlansPage />;
      case 'community':
        return <CommunityPage />;
      case 'profile':
        return <ProfilePage />;
      default:
        return <PlansPage />;
    }
  };

  return (
    <UserOnly>
      <View style={styles.container}>
        {/* 页面内容 */}
        <View style={styles.content}>
          {renderCurrentPage()}
        </View>

        {/* 标签栏 */}
        <View style={[styles.tabBar, { height: showMorePanel ? 150 : 90 }]}>
          {/* More面板 - 三个横向盒子 */}
          {showMorePanel && (
            <Animated.View style={[styles.morePanelRow, morePanelAnimatedStyle]}>
              <TouchableOpacity style={styles.morePanelItem}>
                <ThemedText style={styles.morePanelText}>学习</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.morePanelItem}>
                <ThemedText style={styles.morePanelText}>目标</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.morePanelItem}>
                <ThemedText style={styles.morePanelText}>设置</ThemedText>
              </TouchableOpacity>
            </Animated.View>
          )}

          <View style={styles.tabRow}>
            <Animated.View style={[styles.slider, sliderAnimatedStyle]} />

            {tabItems.map((item, index) => (
              <TouchableOpacity
                key={item.name}
                style={styles.tabButton}
                onPress={() => handleTabPress(item.name)}
                activeOpacity={0.7}
              >
                <View style={styles.tabInnerWrap}>
                  <Animated.View style={iconAnimatedStyles[index]}>
                    {/* 未选中状态的图标 */}
                    <Animated.Image
                      source={item.icon}
                      style={[
                        styles.tabIcon,
                        iconInactiveStyles[index]
                      ]}
                      resizeMode="contain"
                    />
                    {/* 选中状态的图标 */}
                    <Animated.Image
                      source={item.iconActive}
                      style={[
                        styles.tabIcon,
                        {
                          position: 'absolute',
                          top: 0,
                          left: 0,
                        },
                        iconActiveStyles[index]
                      ]}
                      resizeMode="contain"
                    />
                  </Animated.View>
                  <ThemedText style={styles.tabLabel}>{item.title}</ThemedText>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </UserOnly>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#transparent',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.background, // 使用Colors.js中的背景色
    // 移除固定的 marginBottom，让内容能够延伸到底部
  },
  tabBar: {
    flexDirection: 'column',
    paddingBottom: 20, // tabbar下方的20padding
    position: 'absolute',
    bottom: 20, // 底部20间距，让页面内容可见
    left: 20,
    right: 20,
    borderRadius: 55,
    backgroundColor: Colors.button, // 使用Colors.js中的按钮颜色
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1000, // 确保tabbar层级最高
  },
  morePanelRow: {
    flexDirection: 'row',
    height: 50,
    paddingHorizontal: 20,
    paddingVertical: 8,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tabRow: {
    flexDirection: 'row',
    height: 90,
  },
  slider: {
    position: 'absolute',
    top: 15,
    left: 0,
    width: 60,
    height: 60,
    borderRadius: 30,
    zIndex: 0,
    alignSelf: 'center',
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 90,
    zIndex: 1,
  },
  tabInnerWrap: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    width: '100%',
    zIndex: 1,
  },
  tabIcon: {
    width: 28,
    height: 28,
    marginBottom: 2,
    zIndex: 2,
  },
  tabLabel: {
    fontSize: 10,
    color: '#8B4513',
    marginTop: 2,
    fontWeight: '500',
    textAlign: 'center',
  },
  morePanelItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    marginHorizontal: 8,
    backgroundColor: Colors.lightOrange, // 使用Colors.js中的浅橙色
    borderRadius: 16,
    minHeight: 34,
  },
  morePanelText: {
    fontSize: 14,
    color: '#8B4513',
    fontWeight: '500',
    textAlign: 'center',
  },
});


