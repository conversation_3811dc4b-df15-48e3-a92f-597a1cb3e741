import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from "../../components/ThemedText";
import { Colors } from "../../constants/Colors";

const SynchronizedTextbooks = () => {
  const router = useRouter();
  const [expandedChapters, setExpandedChapters] = useState({});

  const toggleChapter = (chapterIndex) => {
    setExpandedChapters(prev => ({
      ...prev,
      [chapterIndex]: !prev[chapterIndex]
    }));
  };

  const chapters = [
    {
      title: "模块一：详解概念——服装设计的构成能力",
      subItems: [
        "1.1 服装设计的基本概念",
        "1.2 设计构成要素分析",
        "1.3 设计思维培养"
      ]
    },
    {
      title: "模块二：外部构型——基础款式设计",
      subItems: [
        "2.1 基础款式分类",
        "2.2 外部轮廓设计"
      ]
    },
    {
      title: "模块三：细节之美——局部要素设计",
      subItems: [
        "3.1 局部细节处理",
        "3.2 装饰元素应用"
      ]
    },
    {
      title: "模块四：美学法则——设计美学方法应用",
      subItems: [
        "4.1 美学原理",
        "4.2 设计方法应用"
      ]
    },
    {
      title: "模块五：精工流程——创作、色彩搭配的",
      subItems: [
        "5.1 创作流程",
        "5.2 色彩搭配技巧"
      ]
    },
    {
      title: "模块六：能力提升——设计方法的综合应用",
      subItems: [
        "6.1 综合设计练习",
        "6.2 能力评估与提升"
      ]
    },
  ];

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 顶部标题栏 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Image source={require('../../assets/FrameTwo.png')} style={[styles.backIcon, { width: 22, height: 22 }]} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>正在学习</ThemedText>
          <View style={styles.headerRight} />
        </View>

        {/* 课程卡片 */}
        <View style={styles.courseCard}>
          <View style={styles.courseImageContainer}>
            <Image
              source={require('../../assets/Index_image/TextbookPhoto.png')}
              style={styles.courseImage}
              resizeMode="cover"
            />
          </View>
          <View style={styles.courseInfo}>
            <ThemedText style={styles.courseTitle}>服装款式设计与表达</ThemedText>
            <View style={styles.courseDetails}>
              <View style={styles.courseDetailRow}>
                <ThemedText style={styles.detailLabel}>主讲</ThemedText>
                <ThemedText style={styles.detailValue}>lulu</ThemedText>
              </View>
              <View style={styles.courseDetailRow}>
                <ThemedText style={styles.detailLabel}>适应群体  </ThemedText>
                <ThemedText style={styles.detailValue}>高等教育适用群体</ThemedText>
              </View>
              <View style={styles.courseDetailRow}>
                <ThemedText style={styles.detailLabel}>出版时间  </ThemedText>
                <ThemedText style={styles.detailValue}>2023年12月31日</ThemedText>
              </View>
              <View style={styles.courseDetailRowSpecial}>
                <ThemedText style={styles.detailValueSpecial}>初次学习2025-06-14</ThemedText>
              </View>
            </View>
            <TouchableOpacity style={styles.continueButton}>
              <ThemedText style={styles.continueButtonText}>继续学习</ThemedText>
            </TouchableOpacity>
          </View>
        </View>

        {/* 课程介绍 */}
        <View style={styles.introSection}>
          <ThemedText style={styles.sectionTitle}>课程介绍</ThemedText>
          <ThemedText style={styles.introText}>
            服装款式设计与表达——门实践性课程，旨在服装设计与工艺专业培养应用型的人才。
          </ThemedText>
          <ThemedText style={styles.introText}>
            本书内容服装设计基础功能力，基础款式设计的制作方法，服装局部要素设计，服装美学方法应用，以及服装设计方法的综合应用等。以实际案例为载体，从服装设计的基础知识入手，详细介绍了服装设计的基本理论、基本方法和基本技能。
          </ThemedText>
          <ThemedText style={styles.introText}>
            本书可作为高等院校服装、纺织、在校大学生教学用书，也可供可可作为服装设计爱好者的参考用书，同时也可作为服装设计专业人员的参考用书。
          </ThemedText>
        </View>

        {/* 目录 */}
        <View style={styles.catalogSection}>
          <ThemedText style={styles.sectionTitle}>目录</ThemedText>
          {chapters.map((chapter, index) => (
            <View key={index}>
              <TouchableOpacity
                style={styles.chapterItem}
                onPress={() => toggleChapter(index)}
              >
                <View style={styles.chapterHeader}>
                  <ThemedText style={styles.chapterTitle}>{chapter.title}</ThemedText>
                  <Image
                    source={expandedChapters[index]
                      ? require('../../assets/Arrows_down.png')
                      : require('../../assets/Arrows_up.png')
                    }
                    style={styles.arrowIcon}
                    resizeMode="contain"
                  />
                </View>
              </TouchableOpacity>

              {/* 展开的子项目 */}
              {expandedChapters[index] && (
                <View style={styles.subItemsContainer}>
                  {chapter.subItems.map((subItem, subIndex) => (
                    <TouchableOpacity key={subIndex} style={styles.subItem}>
                      <ThemedText style={styles.subItemText}>{subItem}</ThemedText>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFF8F3',
    marginTop: 0,
    marginBottom: 4,
  },
  backIcon: {
    fontSize: 24,
    color: '#333',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
  },
  headerRight: {
    width: 24,
  },
  content: {
    flex: 1,
  },
  courseCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    margin: 16,
    padding: 16,
    shadowColor: 'rgba(0, 0, 0, 0.08)',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 1,
    shadowRadius: 16,
    elevation: 6, // Android阴影
    flexDirection: 'row',
  },
  courseImageContainer: {
    marginRight: 16,
  },
  courseImage: {
    width: 120,
    height: 150,
    borderRadius: 8,
  },
  courseInfo: {
    flex: 1,
  },
  courseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
    marginBottom: 12,
  },
  courseDetails: {
    marginBottom: 16,
  },
  courseDetailRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  courseDetailRowSpecial: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    width: 60,
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  detailValueSpecial: {
    fontSize: 14,
    color: '#333',
    textAlign: 'left',
  },
  continueButton: {
    backgroundColor: '#FF8C42',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 8,
    alignSelf: 'flex-end',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  introSection: {
    margin: 16,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title,
    marginBottom: 12,
  },
  introText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  catalogSection: {
    margin: 16,
    marginTop: 0,
  },
  chapterItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  chapterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  chapterTitle: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  arrowIcon: {
    width: 16,
    height: 16,
    marginLeft: 8,
  },
  subItemsContainer: {
    backgroundColor: '#fff',
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 8,
    borderRadius: 8,
    paddingVertical: 8,
  },
  subItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  subItemText: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
});

export default SynchronizedTextbooks;