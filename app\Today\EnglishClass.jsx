import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Text
} from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../components/ThemedText';
import { Colors } from '../../constants/Colors';

const { height } = Dimensions.get('window');

const EnglishClass = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('课程介绍');
  const [isTabFixed, setIsTabFixed] = useState(false);
  const scrollViewRef = useRef(null);
  const tabBarRef = useRef(null);
  const [tabBarY, setTabBarY] = useState(0);

  // 存储每个区域的Y位置
  const [sectionPositions, setSectionPositions] = useState({
    '课程介绍': 0,
    '课程大纲': 0,
    '课程评价': 0
  });

  const tabs = ['课程介绍', '课程大纲', '课程评价'];

  // 处理滚动事件
  const handleScroll = (event) => {
    const scrollY = event.nativeEvent.contentOffset.y;
    const shouldFix = scrollY >= tabBarY - 60; // 60是状态栏高度

    if (shouldFix !== isTabFixed) {
      setIsTabFixed(shouldFix);
    }

    // 根据滚动位置确定当前应该高亮的标签
    if (shouldFix) {
      const adjustedScrollY = scrollY - tabBarY + 60; // 调整后的滚动位置

      if (adjustedScrollY >= sectionPositions['课程评价'] - 50) {
        if (activeTab !== '课程评价') setActiveTab('课程评价');
      } else if (adjustedScrollY >= sectionPositions['课程大纲'] - 50) {
        if (activeTab !== '课程大纲') setActiveTab('课程大纲');
      } else {
        if (activeTab !== '课程介绍') setActiveTab('课程介绍');
      }
    }
  };

  // 点击标签滚动到对应位置
  const scrollToSection = (tab) => {
    if (scrollViewRef.current && sectionPositions[tab] !== undefined) {
      const targetY = tabBarY + sectionPositions[tab] - 60; // 60是固定标签栏的高度
      scrollViewRef.current.scrollTo({ y: targetY, animated: true });
    }
  };

  // 记录区域位置的回调函数
  const onSectionLayout = (tab, event) => {
    const { y } = event.nativeEvent.layout;
    setSectionPositions(prev => ({
      ...prev,
      [tab]: y
    }));
  };

  // 课程介绍内容
  const renderCourseIntro = () => (
    <View style={styles.sectionContainer}>
      <ThemedText style={styles.sectionTitle}>课程介绍</ThemedText>
      <View style={styles.courseHeader}>
        <ThemedText style={styles.courseTitle}>学术英语读写</ThemedText>
        <ThemedText style={styles.courseUniversity}>华中科技大学</ThemedText>
        <ThemedText style={styles.courseParticipants}>3104人参加</ThemedText>
      </View>

      <View style={styles.courseTimeCard}>
        <ThemedText style={styles.courseTimeTitle}>第一次开课</ThemedText>
        <ThemedText style={styles.courseTimeDate}>2024-09-15至2025-8-28</ThemedText>
      </View>

      <View style={styles.courseDescCard}>
        <ThemedText style={styles.sectionTitle}>课程介绍</ThemedText>
        <ThemedText style={styles.courseDesc}>
          本课程基于学术英语读写与教学中"互相促进"的理念，探索如何在学术英语教学中判断学生"以读促写"的学习效果，以及如何和思维的建作为基点，为学习者未来在自己研究领域里完成学术论文写作提供析；这课程在学术英语的读写互动中，帮助学生了解英语学术论文的文体要求，逆辑表达传统，学术论文各要件的其所需要的语言表达与修辞连接等，建立起读者意识；培养学生自然、准确、流畅地阐述事实或证据，表达学术观点等能力以及批判性评价或分析文献的技能。总而言之，该课程的目标是帮助学生学会学术论文的发表，提高我校国际学术竞争力。
        </ThemedText>
      </View>

      <View style={styles.teacherSection}>
        <ThemedText style={styles.sectionTitle}>老师</ThemedText>
        <View style={styles.teacherList}>
          <View style={styles.teacherCard}>
            <View style={styles.teacherAvatar} />
            <View style={styles.teacherInfo}>
              <ThemedText style={styles.teacherName}>刘泽华</ThemedText>
              <ThemedText style={styles.teacherRole}>教授</ThemedText>
            </View>
          </View>
          <View style={styles.teacherCard}>
            <View style={styles.teacherAvatar} />
            <View style={styles.teacherInfo}>
              <ThemedText style={styles.teacherName}>教师2</ThemedText>
              <ThemedText style={styles.teacherRole}>副教授</ThemedText>
            </View>
          </View>
        </View>
      </View>
    </View>
  );

  // 课程大纲内容
  const renderCourseOutline = () => (
    <View style={styles.sectionContainer}>
      <ThemedText style={styles.sectionTitle}>课程大纲</ThemedText>
      <View style={styles.outlineCard}>
        {[
          '01 课程介绍',
          '02 学术英语的山川与海洋学习',
          '03 课程(30分)',
          '04 考试作业',
          '05 期末作业成绩',
          '06 期末、人文学科作业',
          '07 口语与写作'
        ].map((item, index) => (
          <View key={index} style={styles.outlineItem}>
            <ThemedText style={styles.outlineText}>{item}</ThemedText>
            <Image
              source={require('../../assets/Arrows_up.png')}
              style={styles.outlineArrow}
            />
          </View>
        ))}
      </View>
    </View>
  );

  // 课程评价内容
  const renderCourseReview = () => (
    <View style={styles.sectionContainer}>
      <ThemedText style={styles.sectionTitle}>课程评价</ThemedText>
      <View style={styles.reviewCard}>
        <View style={styles.reviewHeader}>
          <View style={styles.ratingContainer}>
            <ThemedText style={styles.ratingScore}>4.6</ThemedText>
            <View style={styles.stars}>
              {[1, 2, 3, 4, 5].map(i => (
                <Text key={i} style={styles.star}>★</Text>
              ))}
            </View>
          </View>
        </View>

        {[
          { name: 'Lol', comment: '很好的课程', rating: 5 },
          { name: 'Sery', comment: '对我的学习很有帮助', rating: 5 },
          { name: '用户3', comment: '内容丰富', rating: 4 },
          { name: '用户4', comment: '老师讲得很清楚，受益匪浅', rating: 5 },
          { name: 'rainbow', comment: '推荐！！', rating: 5 }
        ].map((review, index) => (
          <View key={index} style={styles.reviewItem}>
            <View style={styles.reviewerInfo}>
              <View style={styles.reviewerAvatar} />
              <View style={styles.reviewerDetails}>
                <ThemedText style={styles.reviewerName}>{review.name}</ThemedText>
                <View style={styles.reviewStars}>
                  {[1, 2, 3, 4, 5].map(i => (
                    <Text key={i} style={[styles.star, { color: i <= review.rating ? '#FFD700' : '#DDD' }]}>★</Text>
                  ))}
                </View>
              </View>
            </View>
            <ThemedText style={styles.reviewComment}>{review.comment}</ThemedText>
          </View>
        ))}

        <TouchableOpacity style={styles.writeReviewBtn}>
          <Text style={styles.writeReviewText}>✏️ 课程评论</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 固定的标签栏 - 当滚动到顶部时显示 */}
      {isTabFixed && (
        <View style={styles.fixedTabBar}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
              onPress={() => scrollToSection(tab)}
            >
              <ThemedText style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      )}

      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      >
        {/* 返回按钮 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <View style={styles.headerRight}>
            <TouchableOpacity>
              <Image source={require('../../assets/FrameTwo.png')} style={styles.shareIcon} />
            </TouchableOpacity>
          </View>
        </View>

        {/* 视频播放区域 */}
        <View style={styles.videoContainer}>
          <View style={styles.videoPlayer}>
            {/* 这里可以放置实际的视频播放器 */}
            <View style={styles.videoPlaceholder}>
              <TouchableOpacity style={styles.playButton}>
                <Text style={styles.playButtonText}>▶</Text>
              </TouchableOpacity>
            </View>
            {/* 视频控制栏 */}
            <View style={styles.videoControls}>
              <TouchableOpacity style={styles.pauseBtn}>
                <Text style={styles.controlText}>⏸</Text>
              </TouchableOpacity>
              <View style={styles.progressBar}>
                <View style={styles.progress} />
              </View>
              <ThemedText style={styles.timeText}>01:30:18</ThemedText>
              <TouchableOpacity style={styles.fullscreenBtn}>
                <Text style={styles.controlText}>⛶</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* 标签栏 */}
        <View
          style={styles.tabBar}
          ref={tabBarRef}
          onLayout={(event) => {
            const { y } = event.nativeEvent.layout;
            setTabBarY(y);
          }}
        >
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
              onPress={() => scrollToSection(tab)}
            >
              <ThemedText style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>

        {/* 内容区域 - 连续瀑布流 */}
        <View style={styles.contentContainer}>
          {/* 课程介绍区域 */}
          <View
            onLayout={(event) => onSectionLayout('课程介绍', event)}
          >
            {renderCourseIntro()}
          </View>

          {/* 课程大纲区域 */}
          <View
            onLayout={(event) => onSectionLayout('课程大纲', event)}
          >
            {renderCourseOutline()}
          </View>

          {/* 课程评价区域 */}
          <View
            onLayout={(event) => onSectionLayout('课程评价', event)}
          >
            {renderCourseReview()}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  safeArea: {
    height: 60,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  backIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerRight: {
    flexDirection: 'row',
  },
  shareIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  videoContainer: {
    width: '100%',
    height: height * 0.3,
    backgroundColor: '#000',
    marginTop: 100, // 为header留出空间
  },
  videoPlayer: {
    flex: 1,
    position: 'relative',
  },
  videoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#333',
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255,255,255,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButtonText: {
    fontSize: 24,
    color: '#000',
    marginLeft: 4,
  },
  videoControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: 'rgba(0,0,0,0.7)',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  pauseBtn: {
    marginRight: 12,
  },
  controlText: {
    color: '#fff',
    fontSize: 16,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    marginHorizontal: 12,
  },
  progress: {
    width: '30%',
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  timeText: {
    color: '#fff',
    fontSize: 12,
    marginRight: 12,
  },
  fullscreenBtn: {
    marginLeft: 8,
  },
  fixedTabBar: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: '#FFF8F3',
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    zIndex: 20,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#FFF8F3',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tabItem: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTabItem: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.iconColorFocused,
  },
  tabText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: Colors.light.iconColorFocused,
    fontWeight: 'bold',
  },
  contentContainer: {
    flex: 1,
  },
  sectionContainer: {
    padding: 16,
    marginBottom: 20,
  },
  courseHeader: {
    marginBottom: 16,
  },
  courseTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  courseUniversity: {
    fontSize: 16,
    color: '#FF8C00',
    marginBottom: 4,
  },
  courseParticipants: {
    fontSize: 14,
    color: '#999',
  },
  courseTimeCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  courseTimeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  courseTimeDate: {
    fontSize: 14,
    color: '#666',
  },
  courseDescCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  courseDesc: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
  teacherSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  teacherList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  teacherCard: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  teacherAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E0E0E0',
    marginBottom: 8,
  },
  teacherInfo: {
    alignItems: 'center',
  },
  teacherName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  teacherRole: {
    fontSize: 12,
    color: '#999',
  },
  outlineCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  outlineItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  outlineText: {
    fontSize: 14,
    color: '#333',
  },
  outlineArrow: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  reviewCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingScore: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  stars: {
    flexDirection: 'row',
  },
  star: {
    fontSize: 16,
    color: '#FFD700',
    marginRight: 2,
  },
  reviewItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  reviewerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
    marginRight: 12,
  },
  reviewerDetails: {
    flex: 1,
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  reviewStars: {
    flexDirection: 'row',
  },
  reviewComment: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  writeReviewBtn: {
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  writeReviewText: {
    fontSize: 14,
    color: '#666',
  },
});

export default EnglishClass;