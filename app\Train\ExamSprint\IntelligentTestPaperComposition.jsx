import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ScrollView, Text, Modal, FlatList } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedText from '../../../components/ThemedText';
import ThemedView from '../../../components/ThemedView';
import GradientGlassButton from '../../../components/GradientGlassButton';

const IntelligentTestPaperComposition = () => {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('词汇解析');
  const [showSelectionModal, setShowSelectionModal] = useState(false);
  const [selectedOption, setSelectedOption] = useState('随机抽题');
  const [showTestModeModal, setShowTestModeModal] = useState(false);
  const [selectedTestMode, setSelectedTestMode] = useState('模拟自测');
  const [editModeEnabled, setEditModeEnabled] = useState(false);
  const [moveEnabled, setMoveEnabled] = useState(false);
  const [deleteEnabled, setDeleteEnabled] = useState(false);
  const [selectEnabled, setSelectEnabled] = useState(true);

  const [categories, setCategories] = useState([
    { id: 'triangular', name: '三角函数', color: '#DCECF9', selected: false },
    { id: 'vocabulary', name: '词汇解析', color: '#D8F0D0', selected: true },
    { id: 'grammar', name: '语法结构', color: '#FED8CE', selected: false },
    { id: 'add', name: '+', color: '#FBECC7', selected: false, isAdd: true }
  ]);

  const questionTypes = [
    { type: '单选题', count: 305 },
    { type: '多选题', count: 291 },
    { type: '填空题', count: 132 },
    { type: '判断题', count: 111 }
  ];

  const selectionData = [
    { id: 1, text: '随机抽题', systemImage: '🎲' },
    { id: 2, text: '按题型选题', systemImage: '📝' },
    { id: 3, text: '按难度选题', systemImage: '⭐' },
    { id: 4, text: '按章节选题', systemImage: '📚' },
    { id: 5, text: '错题重练', systemImage: '❌' }
  ];

  const testModeData = [
    { id: 1, text: '模拟自测', systemImage: '📊' },
    { id: 2, text: '刷题自测', systemImage: '📝' },
    { id: 3, text: '限时练习', systemImage: '⏰' },
    { id: 4, text: '闯关模式', systemImage: '🎯' },
    { id: 5, text: '竞赛模式', systemImage: '🏆' },
    { id: 6, text: '复习模式', systemImage: '📖' }
  ];

  const handleSelectionChange = (selectedItems) => {
    alert(`indexes of selected items: ${selectedItems.join(', ')}`);
  };

  const handleMoveItem = (from, to) => {
    alert(`moved item at index ${from} to index ${to}`);
  };

  const handleDeleteItem = (item) => {
    alert(`deleted item at index: ${item}`);
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* 顶部标题栏 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Image source={require('../../../assets/FrameTwo.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>智能组卷</ThemedText>
          <View style={styles.headerRight} />
        </View>

        {/* 分类选择 */}
        <View style={styles.categoryContainer}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryCard,
                { backgroundColor: category.color },
                category.selected && styles.selectedCategory
              ]}
              onPress={() => {
                if (!category.isAdd) {
                  // 更新分类选择状态
                  setCategories(prevCategories =>
                    prevCategories.map(cat => ({
                      ...cat,
                      selected: cat.id === category.id
                    }))
                  );
                  setSelectedCategory(category.name);
                }
              }}
            >
              {category.isAdd ? (
                <Text style={styles.addText}>+</Text>
              ) : (
                <>
                  {category.selected && <Text style={styles.checkMark}>✓</Text>}
                  <Text style={styles.categoryText}>{category.name}</Text>
                </>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* 参数设置 */}
        <View style={styles.settingsSection}>
          <ThemedText style={styles.sectionTitle}>参数设置</ThemedText>

          {/* 从题库选择 */}
          <View style={styles.settingRow}>
            <ThemedText style={styles.settingLabel}>从题库选择</ThemedText>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={() => setShowSelectionModal(true)}
            >
              <ThemedText style={styles.dropdownText}>{selectedOption}</ThemedText>
              <Text style={styles.dropdownArrow}>▼</Text>
            </TouchableOpacity>
          </View>

          {/* 题型选择 */}
          {questionTypes.map((item, index) => (
            <View key={index} style={styles.questionTypeRow}>
              <ThemedText style={styles.questionTypeLabel}>{item.type}</ThemedText>
              <View style={styles.questionTypeRight}>
                <View style={styles.inputContainer}>
                  <View style={styles.numberInput} />
                </View>
                <ThemedText style={styles.questionCount}>/{item.count}</ThemedText>
              </View>
            </View>
          ))}

          {/* 自测模式 */}
          <View style={styles.settingRow}>
            <ThemedText style={styles.settingLabel}>自测模式</ThemedText>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={() => setShowTestModeModal(true)}
            >
              <ThemedText style={styles.dropdownText}>{selectedTestMode}</ThemedText>
              <Text style={styles.dropdownArrow}>▼</Text>
            </TouchableOpacity>
          </View>

          {/* 自测限时 */}
          <View style={styles.settingRow}>
            <ThemedText style={styles.settingLabel}>自测限时</ThemedText>
            <View style={styles.timeInputContainer}>
              <View style={styles.timeInput} />
              <ThemedText style={styles.timeUnit}>分钟</ThemedText>
            </View>
          </View>
        </View>

        {/* 开始按钮 */}
        <View style={styles.buttonContainer}>
          <GradientGlassButton
            title="开始"
            onPress={() => {
              console.log('开始组卷');
              // 这里可以添加开始组卷的逻辑
            }}
            style={styles.startButton}
          />
        </View>

        {/* 自定义列表模态框 */}
        <Modal
          visible={showSelectionModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowSelectionModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>选择题库方式</ThemedText>
                <TouchableOpacity
                  onPress={() => setShowSelectionModal(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>

              <CustomList
                data={selectionData}
                editModeEnabled={editModeEnabled}
                moveEnabled={moveEnabled}
                deleteEnabled={deleteEnabled}
                selectEnabled={selectEnabled}
                onSelectionChange={handleSelectionChange}
                onMoveItem={handleMoveItem}
                onDeleteItem={handleDeleteItem}
                onItemSelect={(item) => {
                  setSelectedOption(item.text);
                  setShowSelectionModal(false);
                }}
              />

              <View style={styles.modalControls}>
                <TouchableOpacity
                  style={[styles.controlButton, editModeEnabled && styles.activeControl]}
                  onPress={() => setEditModeEnabled(!editModeEnabled)}
                >
                  <Text style={styles.controlButtonText}>编辑模式</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.controlButton, moveEnabled && styles.activeControl]}
                  onPress={() => setMoveEnabled(!moveEnabled)}
                >
                  <Text style={styles.controlButtonText}>移动</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.controlButton, deleteEnabled && styles.activeControl]}
                  onPress={() => setDeleteEnabled(!deleteEnabled)}
                >
                  <Text style={styles.controlButtonText}>删除</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* 模拟自测模态框 */}
        <Modal
          visible={showTestModeModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowTestModeModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <ThemedText style={styles.modalTitle}>选择自测模式</ThemedText>
                <TouchableOpacity
                  onPress={() => setShowTestModeModal(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>

              <CustomList
                data={testModeData}
                editModeEnabled={editModeEnabled}
                moveEnabled={moveEnabled}
                deleteEnabled={deleteEnabled}
                selectEnabled={selectEnabled}
                onSelectionChange={handleSelectionChange}
                onMoveItem={handleMoveItem}
                onDeleteItem={handleDeleteItem}
                onItemSelect={(item) => {
                  setSelectedTestMode(item.text);
                  setShowTestModeModal(false);
                }}
              />

              <View style={styles.modalControls}>
                <TouchableOpacity
                  style={[styles.controlButton, editModeEnabled && styles.activeControl]}
                  onPress={() => setEditModeEnabled(!editModeEnabled)}
                >
                  <Text style={styles.controlButtonText}>编辑模式</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.controlButton, moveEnabled && styles.activeControl]}
                  onPress={() => setMoveEnabled(!moveEnabled)}
                >
                  <Text style={styles.controlButtonText}>移动</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.controlButton, deleteEnabled && styles.activeControl]}
                  onPress={() => setDeleteEnabled(!deleteEnabled)}
                >
                  <Text style={styles.controlButtonText}>删除</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </ScrollView>
    </ThemedView>
  );
};

// 自定义列表组件
const CustomList = ({
  data,
  editModeEnabled,
  moveEnabled,
  deleteEnabled,
  selectEnabled,
  onSelectionChange,
  onMoveItem,
  onDeleteItem,
  onItemSelect
}) => {
  const [selectedItems, setSelectedItems] = useState([]);

  const handleItemPress = (item, index) => {
    if (selectEnabled && !editModeEnabled) {
      onItemSelect(item);
    } else if (editModeEnabled) {
      const newSelection = selectedItems.includes(index)
        ? selectedItems.filter(i => i !== index)
        : [...selectedItems, index];
      setSelectedItems(newSelection);
      onSelectionChange(newSelection);
    }
  };

  const handleDelete = (index) => {
    if (deleteEnabled) {
      onDeleteItem(index);
    }
  };

  return (
    <FlatList
      data={data}
      keyExtractor={(item) => item.id.toString()}
      renderItem={({ item, index }) => (
        <LabelPrimitive
          item={item}
          index={index}
          isSelected={selectedItems.includes(index)}
          editMode={editModeEnabled}
          deleteEnabled={deleteEnabled}
          onPress={() => handleItemPress(item, index)}
          onDelete={() => handleDelete(index)}
        />
      )}
      style={styles.customList}
    />
  );
};

// 自定义列表项组件
const LabelPrimitive = ({
  item,
  index,
  isSelected,
  editMode,
  deleteEnabled,
  onPress,
  onDelete
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.listItem,
        isSelected && styles.selectedListItem
      ]}
      onPress={onPress}
    >
      <View style={styles.listItemContent}>
        <Text style={styles.systemImage}>{item.systemImage}</Text>
        <Text style={styles.listItemText}>{item.text}</Text>
      </View>
      {editMode && deleteEnabled && (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={onDelete}
        >
          <Text style={styles.deleteButtonText}>🗑️</Text>
        </TouchableOpacity>
      )}
      {isSelected && (
        <Text style={styles.checkmark}>✓</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 12,
    paddingTop: 32,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRight: {
    width: 32,
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: 80,
    height: 80,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  selectedCategory: {
    borderWidth: 2,
    borderColor: '#D57704',
  },
  checkMark: {
    position: 'absolute',
    top: 8,
    right: 8,
    color: '#D57704',
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  addText: {
    fontSize: 24,
    color: '#999',
    fontWeight: '300',
  },
  settingsSection: {
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 14,
  },
  settingLabel: {
    fontSize: 14,
    color: '#666',
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 100,
  },
  dropdownText: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
  },
  dropdownArrow: {
    fontSize: 10,
    color: '#999',
  },
  questionTypeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 14,
  },
  questionTypeLabel: {
    fontSize: 14,
    color: '#666',
  },
  questionTypeRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputContainer: {
    marginRight: 8,
  },
  numberInput: {
    width: 60,
    height: 30,
    backgroundColor: '#E5E5E5',
    borderRadius: 4,
  },
  questionCount: {
    fontSize: 14,
    color: '#999',
  },
  timeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeInput: {
    width: 80,
    height: 30,
    backgroundColor: '#E5E5E5',
    borderRadius: 4,
    marginRight: 8,
  },
  timeUnit: {
    fontSize: 14,
    color: '#666',
  },
  buttonContainer: {
    paddingHorizontal: 24,
    marginTop: 40,
  },
  startButton: {
    width: '100%',
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FFF8F3',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  customList: {
    maxHeight: 300,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  selectedListItem: {
    backgroundColor: '#E8F5E8',
  },
  listItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  systemImage: {
    fontSize: 20,
    marginRight: 12,
  },
  listItemText: {
    fontSize: 16,
    color: '#333',
  },
  deleteButton: {
    padding: 8,
  },
  deleteButtonText: {
    fontSize: 16,
  },
  checkmark: {
    fontSize: 16,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  modalControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  controlButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    backgroundColor: '#F5F5F5',
  },
  activeControl: {
    backgroundColor: '#4CAF50',
  },
  controlButtonText: {
    fontSize: 14,
    color: '#333',
  },
});

export default IntelligentTestPaperComposition;